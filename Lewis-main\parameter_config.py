VALID_STRATEGY_PARAMETERS = {
    'trend_following': {
        'market_parameters': {
            'signal_strengths': {
                'trend_strength_score': (0.01, 1.0),
                'breakout_strength_multi': (0.1, 5.0),
                'momentum_persistence_bias': (0.0, 1.0)
            },
            'directional_biases': {
                'breakout_direction_confidence': (0.0, 1.0),
                'volatility_regime_bias': (-1.0, 1.0)
            },
            'action_sensitivities': {
                'trailing_stop_sensitivity': (0.001, 0.3),
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0),
                'drawdown_recovery_sensitivity': (0.0, 1.0),
                'time_of_day_sensitivity': (0.0, 1.0)
            }
        },
        'calculation_parameters': {
            'MOVING_AVERAGE_SHORT': (2, 500),
            'MOVING_AVERAGE_LONG': (5, 1000),
            'TREND_CONFIRMATION_PERIOD': (1, 100),
            'BREAKOUT_LEVEL': (0.01, 0.5),

            # ► New additions
            'trend_strength_window': (1, 50),
            'breakout_period': (5, 200),
            'volume_ma_period': (5, 50),
            'atr_period': (5, 100),
        }
    },

    'mean_reversion': {
        'market_parameters': {
            'signal_strengths': {
                'reversion_intensity_score': (0.01, 1.0),
                'volume_mean_reversion_weight': (0.0, 1.0),
                'mean_bias': (-1.0, 1.0)
            },
            'directional_biases': {
                'volatility_regime_bias': (-1.0, 1.0)
            },
            'action_sensitivities': {
                'entry_threshold_sensitivity': (0.01, 0.5),
                'exit_threshold_sensitivity': (0.01, 0.5),
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0),
                'time_decay_sensitivity': (0.0, 1.0)
            }
        },
        'calculation_parameters': {
            'MEAN_WINDOW': (2, 500),
            'STD_MULTIPLIER': (0.1, 10.0),
            'BOLLINGER_PERIOD': (5, 500),
            'BOLLINGER_STD': (0.5, 5.0),

            # ► New additions
            'volume_ma_period': (5, 50),
            'time_decay_rate': (0.90, 0.99),
        }
    },

    'momentum': {
        'market_parameters': {
            'signal_strengths': {
                'momentum_score': (0.01, 1.0),
                'volume_momentum_weight': (0.0, 1.0),
                'momentum_persistence_bias': (0.0, 1.0)
            },
            'directional_biases': {
                'RSI_directional_bias': (-1.0, 1.0),
                'MACD_directional_bias': (-1.0, 1.0),
                'volatility_regime_bias': (-1.0, 1.0)
            },
            'action_sensitivities': {
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0),
                'drawdown_recovery_sensitivity': (0.0, 1.0),
                'noise_filter_sensitivity': (0.0, 1.0)
            }
        },
        'calculation_parameters': {
            'MOMENTUM_PERIOD': (1, 200),
            'RSI_PERIOD': (2, 100),
            'RSI_OVERBOUGHT': (50, 90),
            'RSI_OVERSOLD': (10, 50),
            'MACD_FAST': (5, 50),
            'MACD_SLOW': (10, 200),
            'MACD_SIGNAL': (3, 50),

            # ► New additions
            'volume_ma_period': (5, 50),
            'noise_filter_window': (5, 50),
        }
    },

    'breakout': {
        'market_parameters': {
            'signal_strengths': {
                'breakout_strength_multi': (0.1, 5.0),
                'time_of_day_breakout_weight': (0.0, 1.0)
            },
            'directional_biases': {
                'breakout_direction_confidence': (0.0, 1.0)
            },
            'action_sensitivities': {
                'false_breakout_filter_sensitivity': (0.0, 1.0),
                'post_breakout_retest_sensitivity': (0.0, 1.0),
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0)
            }
        },
        'calculation_parameters': {
            'BREAKOUT_PERIOD': (5, 500),
            'BREAKOUT_THRESHOLD': (0.01, 0.5),
            'VOLUME_CONFIRMATION_MULT': (1.0, 10.0),
            'SUPPORT_RESISTANCE_LOOKBACK': (10, 500),
            'CONSOLIDATION_PERIOD': (5, 100),
            'BREAKOUT_CONFIRMATION_CANDLES': (1, 20),
            'ATR_PERIOD': (5, 100)
        }
    },

    'volatility_clustering': {
        'market_parameters': {
            'signal_strengths': {
                'volatility_extremes_weight': (0.0, 1.0),
                'volatility_autocorr_strength': (0.0, 1.0)
            },
            'directional_biases': {
                'volatility_regime_bias': (-1.0, 1.0),
                'volatility_transition_sensitivity': (0.0, 1.0)
            },
            'action_sensitivities': {
                'volatility_clustering_decay': (0.1, 1.0),
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0)
            }
        },
        'calculation_parameters': {
            'VOLATILITY_WINDOW': (5, 500),
            'HIGH_VOLATILITY_THRESHOLD': (0.1, 5.0),
            'LOW_VOLATILITY_THRESHOLD': (0.01, 1.0),
            'GARCH_LAG': (1, 20),
            'ATR_MULTIPLIER': (0.5, 5.0),
            'VOLATILITY_MEAN_PERIOD': (5, 500),
            'VOLATILITY_BREAKOUT_THRESHOLD': (0.1, 5.0),

            # ► New additions
            'volatility_autocorr_lag': (1, 20),
            'volatility_extremes_window': (5, 100),
        }
    },

    'sentiment_analysis': {
        'market_parameters': {
            'signal_strengths': {
                'sentiment_momentum_score': (-1.0, 1.0),
                'sentiment_volatility_interaction': (0.0, 1.0)
            },
            'directional_biases': {
                'event_driven_bias': (-1.0, 1.0)
            },
            'action_sensitivities': {
                'risk_aversion': (0.0, 1.0),
                'position_sizing_aggressiveness': (0.1, 2.0),
                'drawdown_recovery_sensitivity': (0.0, 1.0)
            }
        },
        'calculation_parameters': {
            'POSITIVE_SENTIMENT_THRESHOLD': (0.5, 1.0),
            'NEGATIVE_SENTIMENT_THRESHOLD': (0.0, 0.5),
            'SENTIMENT_WINDOW': (1, 100),
            'SENTIMENT_IMPACT_WEIGHT': (0.0, 1.0),
            'NEWS_IMPACT_DECAY': (0.1, 1.0),
            'SENTIMENT_SMOOTHING_FACTOR': (0.1, 1.0),
            'SENTIMENT_VOLUME_THRESHOLD': (0.5, 5.0),
            'SENTIMENT_MOMENTUM_PERIOD': (1, 100)
        }
    }
}
