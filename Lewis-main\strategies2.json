[{"name": "Short-Term Trend Follower", "description": "Captures short-term uptrends by using two moving averages and confirming trend strength.", "patterns": ["trend_following"], "parameters": {"market_parameters": {"signal_strengths": {"trend_strength_score": 0.75}, "directional_biases": {"breakout_direction_confidence": 0.6}, "action_sensitivities": {"trailing_stop_sensitivity": 0.01, "risk_aversion": 0.3}}, "calculation_parameters": {"MOVING_AVERAGE_SHORT": 20, "MOVING_AVERAGE_LONG": 50, "TREND_CONFIRMATION_PERIOD": 10, "trend_strength_window": 7}}, "market_conditions": {"optimal_volatility": "0.01-0.05", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "short_term"}, {"name": "Mid-Term Trend Follower", "description": "Follows established mid-term trends with less sensitivity to short-term fluctuations.", "patterns": ["trend_following"], "parameters": {"market_parameters": {"signal_strengths": {"trend_strength_score": 0.6}, "directional_biases": {"breakout_direction_confidence": 0.5}, "action_sensitivities": {"trailing_stop_sensitivity": 0.02, "risk_aversion": 0.4}}, "calculation_parameters": {"MOVING_AVERAGE_SHORT": 50, "MOVING_AVERAGE_LONG": 200, "TREND_CONFIRMATION_PERIOD": 20, "trend_strength_window": 14}}, "market_conditions": {"optimal_volatility": "0.005-0.02", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "mid_term"}, {"name": "Long-Term Trend Follower", "description": "Identifies and follows long-term trends with a focus on significant price movements.", "patterns": ["trend_following"], "parameters": {"market_parameters": {"signal_strengths": {"trend_strength_score": 0.5}, "directional_biases": {"breakout_direction_confidence": 0.4}, "action_sensitivities": {"trailing_stop_sensitivity": 0.03, "risk_aversion": 0.5}}, "calculation_parameters": {"MOVING_AVERAGE_SHORT": 100, "MOVING_AVERAGE_LONG": 500, "TREND_CONFIRMATION_PERIOD": 50, "trend_strength_window": 30}}, "market_conditions": {"optimal_volatility": "0.002-0.01", "optimal_trend": "bullish", "optimal_liquidity": "1.5+"}, "timeframe": "long_term"}, {"name": "Seasonal-Term Trend Follower", "description": "Captures seasonal trends by analyzing historical price patterns and adjusting parameters accordingly.", "patterns": ["trend_following"], "parameters": {"market_parameters": {"signal_strengths": {"trend_strength_score": 0.4}, "directional_biases": {"breakout_direction_confidence": 0.3}, "action_sensitivities": {"trailing_stop_sensitivity": 0.04, "risk_aversion": 0.6}}, "calculation_parameters": {"MOVING_AVERAGE_SHORT": 200, "MOVING_AVERAGE_LONG": 1000, "TREND_CONFIRMATION_PERIOD": 100, "trend_strength_window": 50}}, "market_conditions": {"optimal_volatility": "0.001-0.005", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "seasonal_term"}, {"name": "Short-Term Mean Reversion", "description": "Identifies short-term price deviations from the mean and capitalizes on reversions.", "patterns": ["mean_reversion"], "parameters": {"market_parameters": {"signal_strengths": {"reversion_intensity_score": 0.8, "volume_mean_reversion_weight": 0.5}, "directional_biases": {"mean_bias": 0}, "action_sensitivities": {"entry_threshold_sensitivity": 0.02, "exit_threshold_sensitivity": 0.01, "time_decay_sensitivity": 0.2}}, "calculation_parameters": {"MEAN_WINDOW": 50, "STD_MULTIPLIER": 2.0, "BOLLINGER_PERIOD": 20, "BOLLINGER_STD": 2.0, "time_decay_rate": 0.95}}, "market_conditions": {"optimal_volatility": "0.02-0.10", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "short_term"}, {"name": "Mid-Term Mean Reversion", "description": "Exploits mid-term price oscillations around a moving average using Bollinger Bands.", "patterns": ["mean_reversion"], "parameters": {"market_parameters": {"signal_strengths": {"reversion_intensity_score": 0.7, "volume_mean_reversion_weight": 0.4}, "directional_biases": {"mean_bias": 0}, "action_sensitivities": {"entry_threshold_sensitivity": 0.03, "exit_threshold_sensitivity": 0.02, "time_decay_sensitivity": 0.3}}, "calculation_parameters": {"MEAN_WINDOW": 100, "STD_MULTIPLIER": 1.5, "BOLLINGER_PERIOD": 50, "BOLLINGER_STD": 1.5, "time_decay_rate": 0.96}}, "market_conditions": {"optimal_volatility": "0.01-0.05", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "mid_term"}, {"name": "Long-Term Mean Reversion", "description": "Targets long-term price reversions to a historical average using a wider time window.", "patterns": ["mean_reversion"], "parameters": {"market_parameters": {"signal_strengths": {"reversion_intensity_score": 0.6, "volume_mean_reversion_weight": 0.3}, "directional_biases": {"mean_bias": 0}, "action_sensitivities": {"entry_threshold_sensitivity": 0.04, "exit_threshold_sensitivity": 0.03, "time_decay_sensitivity": 0.4}}, "calculation_parameters": {"MEAN_WINDOW": 200, "STD_MULTIPLIER": 1.0, "BOLLINGER_PERIOD": 100, "BOLLINGER_STD": 1.0, "time_decay_rate": 0.97}}, "market_conditions": {"optimal_volatility": "0.005-0.02", "optimal_trend": "range", "optimal_liquidity": "1.5+"}, "timeframe": "long_term"}, {"name": "Seasonal-Term Mean Reversion", "description": "Identifies and trades seasonal price reversions based on historical data.", "patterns": ["mean_reversion"], "parameters": {"market_parameters": {"signal_strengths": {"reversion_intensity_score": 0.5, "volume_mean_reversion_weight": 0.2}, "directional_biases": {"mean_bias": 0}, "action_sensitivities": {"entry_threshold_sensitivity": 0.05, "exit_threshold_sensitivity": 0.04, "time_decay_sensitivity": 0.5}}, "calculation_parameters": {"MEAN_WINDOW": 500, "STD_MULTIPLIER": 0.5, "BOLLINGER_PERIOD": 200, "BOLLINGER_STD": 0.5, "time_decay_rate": 0.98}}, "market_conditions": {"optimal_volatility": "0.002-0.01", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "seasonal_term"}, {"name": "Short-Term Momentum", "description": "Capitalizes on short bursts of price momentum using RSI and MACD indicators.", "patterns": ["momentum"], "parameters": {"market_parameters": {"signal_strengths": {"momentum_score": 0.9, "volume_momentum_weight": 0.7}, "directional_biases": {"RSI_directional_bias": 0.5, "MACD_directional_bias": 0.6, "volatility_regime_bias": 0.3}, "action_sensitivities": {"noise_filter_sensitivity": 0.2, "risk_aversion": 0.2, "position_sizing_aggressiveness": 1.5, "drawdown_recovery_sensitivity": 0.5, "time_of_day_sensitivity": 0.3}}, "calculation_parameters": {"MOMENTUM_PERIOD": 10, "RSI_PERIOD": 14, "RSI_OVERBOUGHT": 70, "RSI_OVERSOLD": 30, "MACD_FAST": 12, "MACD_SLOW": 26, "MACD_SIGNAL": 9, "noise_filter_window": 5}}, "market_conditions": {"optimal_volatility": "0.05-0.20", "optimal_trend": "bullish", "optimal_liquidity": "≥1.5"}, "timeframe": "short_term"}, {"name": "Mid-Term Momentum", "description": "Follows sustained momentum trends with a longer lookback period and adjusted RSI/MACD parameters.", "patterns": ["momentum"], "parameters": {"market_parameters": {"signal_strengths": {"momentum_score": 0.8, "volume_momentum_weight": 0.6}, "directional_biases": {"RSI_directional_bias": 0.4, "MACD_directional_bias": 0.5, "volatility_regime_bias": 0.2}, "action_sensitivities": {"noise_filter_sensitivity": 0.3, "risk_aversion": 0.3, "position_sizing_aggressiveness": 1.4, "drawdown_recovery_sensitivity": 0.6, "time_of_day_sensitivity": 0.4}}, "calculation_parameters": {"MOMENTUM_PERIOD": 20, "RSI_PERIOD": 21, "RSI_OVERBOUGHT": 75, "RSI_OVERSOLD": 25, "MACD_FAST": 16, "MACD_SLOW": 32, "MACD_SIGNAL": 12, "noise_filter_window": 10}}, "market_conditions": {"optimal_volatility": "0.02-0.10", "optimal_trend": "bullish", "optimal_liquidity": "≥1.2"}, "timeframe": "mid_term"}, {"name": "Long-Term Momentum", "description": "Identifies long-term momentum shifts using a larger lookback period and less sensitive indicators.", "patterns": ["momentum"], "parameters": {"market_parameters": {"signal_strengths": {"momentum_score": 0.7, "volume_momentum_weight": 0.5}, "directional_biases": {"RSI_directional_bias": 0.3, "MACD_directional_bias": 0.4, "volatility_regime_bias": 0.1}, "action_sensitivities": {"noise_filter_sensitivity": 0.4, "risk_aversion": 0.4, "position_sizing_aggressiveness": 1.3, "drawdown_recovery_sensitivity": 0.7, "time_of_day_sensitivity": 0.5}}, "calculation_parameters": {"MOMENTUM_PERIOD": 50, "RSI_PERIOD": 28, "RSI_OVERBOUGHT": 80, "RSI_OVERSOLD": 20, "MACD_FAST": 20, "MACD_SLOW": 40, "MACD_SIGNAL": 18, "noise_filter_window": 15}}, "market_conditions": {"optimal_volatility": "0.01-0.05", "optimal_trend": "bullish", "optimal_liquidity": "≥1.0"}, "timeframe": "long_term"}, {"name": "Seasonal-Term Momentum", "description": "Captures long-term seasonal momentum shifts with a focus on historical patterns.", "patterns": ["momentum"], "parameters": {"market_parameters": {"signal_strengths": {"momentum_score": 0.6, "volume_momentum_weight": 0.4}, "directional_biases": {"RSI_directional_bias": 0.2, "MACD_directional_bias": 0.3, "volatility_regime_bias": 0}, "action_sensitivities": {"noise_filter_sensitivity": 0.5, "risk_aversion": 0.5, "position_sizing_aggressiveness": 1.2, "drawdown_recovery_sensitivity": 0.8, "time_of_day_sensitivity": 0.6}}, "calculation_parameters": {"MOMENTUM_PERIOD": 100, "RSI_PERIOD": 35, "RSI_OVERBOUGHT": 85, "RSI_OVERSOLD": 15, "MACD_FAST": 25, "MACD_SLOW": 50, "MACD_SIGNAL": 24, "noise_filter_window": 20}}, "market_conditions": {"optimal_volatility": "0.005-0.02", "optimal_trend": "bullish", "optimal_liquidity": "≥0.8"}, "timeframe": "seasonal_term"}, {"name": "Short-Term Breakout", "description": "Identifies and trades short-term breakouts from consolidation periods.", "patterns": ["breakout"], "parameters": {"market_parameters": {"signal_strengths": {"breakout_strength_multi": 0.5}, "directional_biases": {"breakout_direction_confidence": 0.7, "volatility_regime_bias": -0.5}, "action_sensitivities": {"false_breakout_filter_sensitivity": 0.2, "post_breakout_retest_sensitivity": 0.1}}, "calculation_parameters": {"BREAKOUT_PERIOD": 10, "BREAKOUT_THRESHOLD": 0.02, "VOLUME_CONFIRMATION_MULT": 1.5, "SUPPORT_RESISTANCE_LOOKBACK": 20, "CONSOLIDATION_PERIOD": 5, "BREAKOUT_CONFIRMATION_CANDLES": 2, "ATR_PERIOD": 14, "breakout_period": 15, "volume_ma_period": 7}}, "market_conditions": {"optimal_volatility": "0.01-0.05", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "short_term"}, {"name": "Mid-Term Breakout", "description": "Trades breakouts from established trading ranges using support/resistance levels.", "patterns": ["breakout"], "parameters": {"market_parameters": {"signal_strengths": {"breakout_strength_multi": 1.0}, "directional_biases": {"breakout_direction_confidence": 0.6, "volatility_regime_bias": -0.4}, "action_sensitivities": {"false_breakout_filter_sensitivity": 0.3, "post_breakout_retest_sensitivity": 0.2}}, "calculation_parameters": {"BREAKOUT_PERIOD": 20, "BREAKOUT_THRESHOLD": 0.03, "VOLUME_CONFIRMATION_MULT": 2.0, "SUPPORT_RESISTANCE_LOOKBACK": 50, "CONSOLIDATION_PERIOD": 10, "BREAKOUT_CONFIRMATION_CANDLES": 3, "ATR_PERIOD": 20, "breakout_period": 30, "volume_ma_period": 14}}, "market_conditions": {"optimal_volatility": "0.005-0.02", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "mid_term"}, {"name": "Long-Term Breakout", "description": "Captures major breakouts from long-term consolidation patterns with stricter confirmation criteria.", "patterns": ["breakout"], "parameters": {"market_parameters": {"signal_strengths": {"breakout_strength_multi": 1.5}, "directional_biases": {"breakout_direction_confidence": 0.5, "volatility_regime_bias": -0.3}, "action_sensitivities": {"false_breakout_filter_sensitivity": 0.4, "post_breakout_retest_sensitivity": 0.3}}, "calculation_parameters": {"BREAKOUT_PERIOD": 50, "BREAKOUT_THRESHOLD": 0.04, "VOLUME_CONFIRMATION_MULT": 2.5, "SUPPORT_RESISTANCE_LOOKBACK": 100, "CONSOLIDATION_PERIOD": 20, "BREAKOUT_CONFIRMATION_CANDLES": 4, "ATR_PERIOD": 28, "breakout_period": 60, "volume_ma_period": 20}}, "market_conditions": {"optimal_volatility": "0.002-0.01", "optimal_trend": "range", "optimal_liquidity": "1.5+"}, "timeframe": "long_term"}, {"name": "Seasonal-Term Breakout", "description": "Identifies and trades breakouts based on seasonal patterns and historical data.", "patterns": ["breakout"], "parameters": {"market_parameters": {"signal_strengths": {"breakout_strength_multi": 2.0}, "directional_biases": {"breakout_direction_confidence": 0.4, "volatility_regime_bias": -0.2}, "action_sensitivities": {"false_breakout_filter_sensitivity": 0.5, "post_breakout_retest_sensitivity": 0.4}}, "calculation_parameters": {"BREAKOUT_PERIOD": 100, "BREAKOUT_THRESHOLD": 0.05, "VOLUME_CONFIRMATION_MULT": 3.0, "SUPPORT_RESISTANCE_LOOKBACK": 200, "CONSOLIDATION_PERIOD": 50, "BREAKOUT_CONFIRMATION_CANDLES": 5, "ATR_PERIOD": 35, "breakout_period": 120, "volume_ma_period": 30}}, "market_conditions": {"optimal_volatility": "0.001-0.005", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "seasonal_term"}, {"name": "Short-Term Volatility Clustering", "description": "Capitalizes on short-term volatility bursts and lulls by dynamically adjusting risk parameters.", "patterns": ["volatility_clustering"], "parameters": {"market_parameters": {"signal_strengths": {"volatility_extremes_weight": 0.7, "volatility_autocorr_strength": 0.6}, "directional_biases": {"volatility_regime_bias": 0.5}, "action_sensitivities": {"volatility_transition_sensitivity": 0.3, "volatility_clustering_decay": 0.2}}, "calculation_parameters": {"VOLATILITY_WINDOW": 20, "HIGH_VOLATILITY_THRESHOLD": 0.3, "LOW_VOLATILITY_THRESHOLD": 0.05, "GARCH_LAG": 5, "ATR_MULTIPLIER": 1.0, "VOLATILITY_MEAN_PERIOD": 10, "VOLATILITY_BREAKOUT_THRESHOLD": 0.2, "volatility_autocorr_lag": 3, "volatility_extremes_window": 10}}, "market_conditions": {"optimal_volatility": "0.05-0.30", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "short_term"}, {"name": "Mid-Term Volatility Clustering", "description": "Adapts to changing mid-term volatility regimes using GARCH and ATR indicators.", "patterns": ["volatility_clustering"], "parameters": {"market_parameters": {"signal_strengths": {"volatility_extremes_weight": 0.6, "volatility_autocorr_strength": 0.5}, "directional_biases": {"volatility_regime_bias": 0.4}, "action_sensitivities": {"volatility_transition_sensitivity": 0.4, "volatility_clustering_decay": 0.3}}, "calculation_parameters": {"VOLATILITY_WINDOW": 50, "HIGH_VOLATILITY_THRESHOLD": 0.2, "LOW_VOLATILITY_THRESHOLD": 0.02, "GARCH_LAG": 10, "ATR_MULTIPLIER": 1.5, "VOLATILITY_MEAN_PERIOD": 20, "VOLATILITY_BREAKOUT_THRESHOLD": 0.15, "volatility_autocorr_lag": 5, "volatility_extremes_window": 20}}, "market_conditions": {"optimal_volatility": "0.02-0.10", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "mid_term"}, {"name": "Long-Term Volatility Clustering", "description": "Identifies and trades long-term volatility shifts with a focus on sustained changes.", "patterns": ["volatility_clustering"], "parameters": {"market_parameters": {"signal_strengths": {"volatility_extremes_weight": 0.5, "volatility_autocorr_strength": 0.4}, "directional_biases": {"volatility_regime_bias": 0.3}, "action_sensitivities": {"volatility_transition_sensitivity": 0.5, "volatility_clustering_decay": 0.4}}, "calculation_parameters": {"VOLATILITY_WINDOW": 100, "HIGH_VOLATILITY_THRESHOLD": 0.15, "LOW_VOLATILITY_THRESHOLD": 0.01, "GARCH_LAG": 15, "ATR_MULTIPLIER": 2.0, "VOLATILITY_MEAN_PERIOD": 50, "VOLATILITY_BREAKOUT_THRESHOLD": 0.1, "volatility_autocorr_lag": 10, "volatility_extremes_window": 50}}, "market_conditions": {"optimal_volatility": "0.01-0.05", "optimal_trend": "range", "optimal_liquidity": "1.5+"}, "timeframe": "long_term"}, {"name": "Seasonal-Term Volatility Clustering", "description": "Analyzes seasonal volatility patterns and adjusts risk management accordingly.", "patterns": ["volatility_clustering"], "parameters": {"market_parameters": {"signal_strengths": {"volatility_extremes_weight": 0.4, "volatility_autocorr_strength": 0.3}, "directional_biases": {"volatility_regime_bias": 0.2}, "action_sensitivities": {"volatility_transition_sensitivity": 0.6, "volatility_clustering_decay": 0.5}}, "calculation_parameters": {"VOLATILITY_WINDOW": 200, "HIGH_VOLATILITY_THRESHOLD": 0.1, "LOW_VOLATILITY_THRESHOLD": 0.005, "GARCH_LAG": 20, "ATR_MULTIPLIER": 2.5, "VOLATILITY_MEAN_PERIOD": 100, "VOLATILITY_BREAKOUT_THRESHOLD": 0.05, "volatility_autocorr_lag": 15, "volatility_extremes_window": 100}}, "market_conditions": {"optimal_volatility": "0.005-0.02", "optimal_trend": "range", "optimal_liquidity": "1.2+"}, "timeframe": "seasonal_term"}, {"name": "Short-Term Sentiment Analysis", "description": "Reacts to short-term sentiment shifts using news sentiment and social media data.", "patterns": ["sentiment_analysis"], "parameters": {"market_parameters": {"signal_strengths": {"sentiment_momentum_score": 0.8, "sentiment_volatility_interaction": 0.6}, "directional_biases": {"event_driven_bias": 0.5}, "action_sensitivities": {}}, "calculation_parameters": {"POSITIVE_SENTIMENT_THRESHOLD": 0.7, "NEGATIVE_SENTIMENT_THRESHOLD": 0.3, "SENTIMENT_WINDOW": 20, "SENTIMENT_IMPACT_WEIGHT": 0.5, "NEWS_IMPACT_DECAY": 0.2, "SENTIMENT_SMOOTHING_FACTOR": 0.3, "SENTIMENT_VOLUME_THRESHOLD": 1.0, "SENTIMENT_MOMENTUM_PERIOD": 10}}, "market_conditions": {"optimal_volatility": "0.05-0.20", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "short_term"}, {"name": "Mid-Term Sentiment Analysis", "description": "Trades based on mid-term sentiment trends with a focus on sustained sentiment changes.", "patterns": ["sentiment_analysis"], "parameters": {"market_parameters": {"signal_strengths": {"sentiment_momentum_score": 0.7, "sentiment_volatility_interaction": 0.5}, "directional_biases": {"event_driven_bias": 0.4}, "action_sensitivities": {}}, "calculation_parameters": {"POSITIVE_SENTIMENT_THRESHOLD": 0.6, "NEGATIVE_SENTIMENT_THRESHOLD": 0.4, "SENTIMENT_WINDOW": 50, "SENTIMENT_IMPACT_WEIGHT": 0.4, "NEWS_IMPACT_DECAY": 0.3, "SENTIMENT_SMOOTHING_FACTOR": 0.4, "SENTIMENT_VOLUME_THRESHOLD": 1.5, "SENTIMENT_MOMENTUM_PERIOD": 20}}, "market_conditions": {"optimal_volatility": "0.02-0.10", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "mid_term"}, {"name": "Long-Term Sentiment Analysis", "description": "Identifies and trades based on long-term sentiment shifts using various data sources.", "patterns": ["sentiment_analysis"], "parameters": {"market_parameters": {"signal_strengths": {"sentiment_momentum_score": 0.6, "sentiment_volatility_interaction": 0.4}, "directional_biases": {"event_driven_bias": 0.3}, "action_sensitivities": {}}, "calculation_parameters": {"POSITIVE_SENTIMENT_THRESHOLD": 0.8, "NEGATIVE_SENTIMENT_THRESHOLD": 0.2, "SENTIMENT_WINDOW": 100, "SENTIMENT_IMPACT_WEIGHT": 0.3, "NEWS_IMPACT_DECAY": 0.4, "SENTIMENT_SMOOTHING_FACTOR": 0.5, "SENTIMENT_VOLUME_THRESHOLD": 2.0, "SENTIMENT_MOMENTUM_PERIOD": 50}}, "market_conditions": {"optimal_volatility": "0.02-0.10", "optimal_trend": "bullish", "optimal_liquidity": "1.2+"}, "timeframe": "long_term"}]