#!/usr/bin/env python3
"""
Test script for the strategy factory functionality
"""
import json
import os
import shutil
from typing import Dict, Any

def backup_strategies_file():
    """Create a backup of the strategies file"""
    if os.path.exists('strategies.json'):
        shutil.copy('strategies.json', 'strategies_backup.json')
        print("✅ Created backup of strategies.json")
        return True
    return False

def restore_strategies_file():
    """Restore the strategies file from backup"""
    if os.path.exists('strategies_backup.json'):
        shutil.copy('strategies_backup.json', 'strategies.json')
        os.remove('strategies_backup.json')
        print("✅ Restored strategies.json from backup")
        return True
    return False

def test_strategy_factory_add_strategy():
    """Test adding a new strategy using the strategy factory pattern"""
    print("Testing Strategy Factory - Add New Strategy...")
    
    # Create a new test strategy
    new_strategy = {
        "name": "Test Momentum Strategy",
        "description": "A test momentum strategy for validation purposes",
        "patterns": ["momentum"],
        "parameters": {
            "market_parameters": {
                "signal_strengths": {
                    "momentum_score": 0.85,
                    "volume_momentum_weight": 0.65,
                    "momentum_persistence_bias": 0.75
                },
                "directional_biases": {
                    "RSI_directional_bias": 0.3,
                    "MACD_directional_bias": 0.4,
                    "volatility_regime_bias": 0.2
                },
                "action_sensitivities": {
                    "noise_filter_sensitivity": 0.25,
                    "risk_aversion": 0.35,
                    "position_sizing_aggressiveness": 1.4,
                    "drawdown_recovery_sensitivity": 0.6
                }
            },
            "calculation_parameters": {
                "MOMENTUM_PERIOD": 15,
                "RSI_PERIOD": 14,
                "RSI_OVERBOUGHT": 75,
                "RSI_OVERSOLD": 25,
                "MACD_FAST": 12,
                "MACD_SLOW": 26,
                "MACD_SIGNAL": 9,
                "volume_ma_period": 20,
                "noise_filter_window": 10
            }
        },
        "timeframe": "short_term",
        "market_conditions": {
            "optimal_volatility": "0.08-0.25",
            "optimal_trend": "bullish",
            "optimal_liquidity": "≥1.3"
        }
    }
    
    try:
        # Load existing strategies
        with open('strategies.json', 'r') as f:
            strategies = json.load(f)
        
        original_count = len(strategies)
        print(f"📊 Original strategy count: {original_count}")
        
        # Generate a unique key for the new strategy
        import hashlib
        strategy_key = f"test_momentum_{hashlib.md5(json.dumps(new_strategy, sort_keys=True).encode()).hexdigest()[:6]}"
        
        # Add the new strategy
        strategies[strategy_key] = new_strategy
        
        # Save back to file
        with open('strategies.json', 'w') as f:
            json.dump(strategies, f, indent=4)
        
        print(f"✅ Added new strategy with key: {strategy_key}")
        print(f"📊 New strategy count: {len(strategies)}")
        
        # Verify the strategy was added correctly
        with open('strategies.json', 'r') as f:
            updated_strategies = json.load(f)
        
        if strategy_key in updated_strategies:
            added_strategy = updated_strategies[strategy_key]
            if added_strategy['name'] == new_strategy['name']:
                print("✅ Strategy was successfully added and verified")
                return True, strategy_key
            else:
                print("❌ Strategy was added but data doesn't match")
                return False, None
        else:
            print("❌ Strategy was not found after adding")
            return False, None
            
    except Exception as e:
        print(f"❌ Failed to add strategy: {e}")
        return False, None

def test_strategy_validation():
    """Test strategy validation logic"""
    print("\nTesting Strategy Validation...")
    
    # Test valid strategy
    valid_strategy = {
        "name": "Valid Test Strategy",
        "description": "A valid test strategy",
        "patterns": ["trend_following"],
        "parameters": {
            "market_parameters": {
                "signal_strengths": {"trend_strength_score": 0.5},
                "directional_biases": {"volatility_regime_bias": 0.0},
                "action_sensitivities": {"risk_aversion": 0.3}
            },
            "calculation_parameters": {
                "MOVING_AVERAGE_SHORT": 10,
                "MOVING_AVERAGE_LONG": 20
            }
        },
        "timeframe": "short_term",
        "market_conditions": {
            "optimal_volatility": "0.01-0.05",
            "optimal_trend": "bullish",
            "optimal_liquidity": "1.2+"
        }
    }
    
    # Test invalid strategy (missing required fields)
    invalid_strategy = {
        "name": "Invalid Test Strategy",
        "description": "Missing required fields"
        # Missing patterns, parameters, timeframe, market_conditions
    }
    
    def validate_strategy(strategy):
        """Simple validation function"""
        required_fields = ['name', 'description', 'patterns', 'parameters', 'timeframe', 'market_conditions']
        
        for field in required_fields:
            if field not in strategy:
                return False, f"Missing required field: {field}"
        
        # Check parameters structure
        params = strategy['parameters']
        if 'market_parameters' not in params or 'calculation_parameters' not in params:
            return False, "Invalid parameters structure"
        
        # Check market_parameters subgroups
        market_params = params['market_parameters']
        required_subgroups = ['signal_strengths', 'directional_biases', 'action_sensitivities']
        for subgroup in required_subgroups:
            if subgroup not in market_params:
                return False, f"Missing market parameter subgroup: {subgroup}"
        
        return True, "Valid"
    
    # Test valid strategy
    is_valid, message = validate_strategy(valid_strategy)
    if is_valid:
        print("✅ Valid strategy passed validation")
    else:
        print(f"❌ Valid strategy failed validation: {message}")
        return False
    
    # Test invalid strategy
    is_valid, message = validate_strategy(invalid_strategy)
    if not is_valid:
        print(f"✅ Invalid strategy correctly rejected: {message}")
    else:
        print("❌ Invalid strategy incorrectly passed validation")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Starting Strategy Factory Tests\n")
    
    # Create backup
    backup_created = backup_strategies_file()
    
    try:
        # Run tests
        tests_passed = 0
        total_tests = 2
        
        # Test 1: Strategy validation
        if test_strategy_validation():
            tests_passed += 1
        
        # Test 2: Add new strategy
        success, strategy_key = test_strategy_factory_add_strategy()
        if success:
            tests_passed += 1
            
            # Clean up - remove the test strategy
            try:
                with open('strategies.json', 'r') as f:
                    strategies = json.load(f)
                
                if strategy_key in strategies:
                    del strategies[strategy_key]
                    
                    with open('strategies.json', 'w') as f:
                        json.dump(strategies, f, indent=4)
                    
                    print(f"🧹 Cleaned up test strategy: {strategy_key}")
            except Exception as e:
                print(f"⚠️  Failed to clean up test strategy: {e}")
        
        print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All strategy factory tests passed!")
            return True
        else:
            print("⚠️  Some tests failed.")
            return False
            
    finally:
        # Restore backup if it was created
        if backup_created:
            restore_strategies_file()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
