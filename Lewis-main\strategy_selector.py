import google.generativeai as genai
import pandas as pd
import os
from typing import Dict, List
from strategy import Strategy, TimeFrame
import prophet as Prophet
from functools import lru_cache
import json
import asyncio

class StrategySelector:
    def __init__(self, config, api_call_manager):
        self.config = config
        self.api_call_manager = api_call_manager
        genai.configure(api_key=os.environ['GOOGLE_AI_API_KEY'])
        self.model = genai.GenerativeModel('models/gemini-1.5-pro')
        self.prophet_model = Prophet.Prophet(daily_seasonality=True)
        self.prediction_horizon = {
            TimeFrame.SHORT_TERM: 1,
            TimeFrame.MID_TERM: 7,
            TimeFrame.LONG_TERM: 90,
            TimeFrame.SEASONAL_TERM: 365
        }

    @lru_cache(maxsize=100)
    def generate_predictions(self, time_frame: TimeFrame, market_data: pd.DataFrame):
        df = market_data[['close']].reset_index()
        df.columns = ['ds', 'y']
        self.prophet_model.fit(df)
        future = self.prophet_model.make_future_dataframe(periods=self.prediction_horizon[time_frame])
        forecast = self.prophet_model.predict(future)
        return forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
    
    async def select_candidate_strategies(
    self,
    strategies: Dict[TimeFrame, List[Strategy]],
    strategy_performance: Dict[str, Dict],
    market_data: pd.DataFrame,
    asset_position_value: float
) -> Dict[TimeFrame, List[Strategy]]:
        """
        AI selects 3–5 best strategies per timeframe using detailed market and strategy data.
        """
        prompt = self._create_detailed_prompt(strategies, strategy_performance, market_data, asset_position_value)

        response = self.model.generate_content(prompt)
        
        try:
            lines = response.text.strip().splitlines()
            selected_strategies = {}

            for line in lines:
                if ':' not in line:
                    continue

                tf_key, indices_str = line.lower().split(':', 1)
                tf_enum = TimeFrame(tf_key.strip())
                indices = [int(i.strip()) - 1 for i in indices_str.split(',') if i.strip().isdigit()]

                if tf_enum not in strategies:
                    continue

                strat_list = strategies[tf_enum]
                selected = [strat_list[i] for i in indices if 0 <= i < len(strat_list)]

                # Enforce 3–5 strategy rule
                if len(selected) < 3:
                    remaining = [s for s in strat_list if s not in selected][:3 - len(selected)]
                    selected.extend(remaining)

                if len(selected) > 5:
                    selected = selected[:5]

                selected_strategies[tf_enum] = selected

            return selected_strategies

        except Exception as e:
            self.logger.error(f"Failed to parse AI response: {e}")
            return {tf: strategies[tf][:3] for tf in strategies if strategies[tf]}  # fallback to top 3

    def _create_prompt(
    self,
    strategies: Dict[TimeFrame, List[Strategy]],
    strategy_performance: Dict[str, Dict],
    market_data: pd.DataFrame,
    asset_position_value: float
    ) -> str:
        prompt = "You are an AI responsible for selecting optimal trading strategies for different timeframes.\n\n"
        prompt += "### Current Market Data:\n"
        prompt += f"- Close Prices (Last 5): {market_data['close'].tail().to_dict()}\n"
        prompt += f"- Volume (Last 5): {market_data['volume'].tail().to_dict()}\n"
        prompt += f"- 50-day MA: {market_data['close'].rolling(50).mean().iloc[-1]:.2f}\n"
        prompt += f"- RSI (14): {self.calculate_rsi(market_data['close'], 14).iloc[-1]:.2f}\n"
    
        bb = self.calculate_bollinger_bands(market_data['close'])
        prompt += f"- Bollinger Bands (Last 5): {bb.tail().to_dict()}\n"
        macd = self.calculate_macd(market_data['close'])
        prompt += f"- MACD (Last 5): {macd.tail().to_dict()}\n"
        prompt += f"- Asset Position Value: ${asset_position_value:.2f}\n"

        prompt += "\n### Strategy Pool:\n"
        for time_frame in TimeFrame:
            tf_strategies = strategies.get(time_frame, [])
            prompt += f"\n[{time_frame.value.upper()}]\n"

            predictions = self.generate_predictions(time_frame, market_data).iloc[-1]
            prompt += f"- Forecast: {predictions['yhat']:.2f} (Range: {predictions['yhat_lower']:.2f} to {predictions['yhat_upper']:.2f})\n"

            for i, strategy in enumerate(tf_strategies, start=1):
                perf = strategy_performance.get(strategy.name, {})
                prompt += f"{i}. {strategy.name}\n"
                prompt += f"   Patterns: {', '.join(strategy.favored_patterns)}\n"
                prompt += f"   Params: {strategy.parameters}\n"
                prompt += f"   Sharpe: {perf.get('sharpe_ratio', 'N/A')}, "
                prompt += f"Profit Factor: {perf.get('profit_factor', 'N/A')}, "
                prompt += f"Win Rate: {perf.get('win_rate', 'N/A')}\n"

        prompt += "\n### Task:\n"
        prompt += "Select the **3 to 5 best strategies** for each timeframe based on market indicators, performance, and forecast.\n"
        prompt += "Return your selection **by index**, in the format below:\n"
        prompt += "short_term: 1,2,3\nmid_term: 1,2,4\nlong_term: 3,4,5\nseasonal: 1,3\n"
        prompt += "Do NOT select poor strategies just to fill the count. Only 3–5 per timeframe max.\n"

        return prompt

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, num_std: int = 2) -> pd.DataFrame:
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return pd.DataFrame({'upper': upper_band, 'middle': sma, 'lower': lower_band})

    def calculate_macd(self, prices: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> pd.DataFrame:
        fast_ema = prices.ewm(span=fast_period, adjust=False).mean()
        slow_ema = prices.ewm(span=slow_period, adjust=False).mean()
        macd = fast_ema - slow_ema
        signal = macd.ewm(span=signal_period, adjust=False).mean()
        histogram = macd - signal
        return pd.DataFrame({'macd': macd, 'signal': signal, 'histogram': histogram})

    def _get_timeframe_parameters(self, timeframe):
        return {
            TimeFrame.SHORT_TERM: {
                'data_points': 1440,  # Minutes in a day
                'prediction_window': 60,  # 1 hour
                'feature_importance_threshold': 0.8
            },
            TimeFrame.MID_TERM: {
                'data_points': 504,   # Hours in 3 weeks
                'prediction_window': 168,  # 1 week
                'feature_importance_threshold': 0.7
            },
            TimeFrame.LONG_TERM: {
                'data_points': 365,   # Days in a year
                'prediction_window': 30,   # 1 month
                'feature_importance_threshold': 0.6
            },
            TimeFrame.SEASONAL_TERM: {
                'data_points': 1095,  # Days in 3 years
                'prediction_window': 365,  # 1 year
                'feature_importance_threshold': 0.5
            }
        }[timeframe]
