import asyncio
import time
import logging
import uuid
from typing import Callable, Awaitable, Dict, Any

class APICallManager:
    def __init__(self, rate_limit_seconds: float = 60.0):
        self.logger = logging.getLogger("api_call_manager")
        self.rate_limit = rate_limit_seconds
        self.queue: asyncio.Queue = asyncio.Queue()
        self.task_map: Dict[str, asyncio.Future] = {}
        self.last_call_time = 0.0
        self.worker = None
        self.running = False

    async def start(self):
        if not self.running:
            self.running = True
            self.logger.info("[Queue] Attempting to start worker...")
            self.worker = asyncio.create_task(self._worker_loop())
            self.logger.info("[Queue] Worker started.")

    async def stop(self):
        self.running = False
        if self.worker:
            self.worker.cancel()
            try:
                await self.worker
            except asyncio.CancelledError:
                self.logger.info("[Queue] Worker stopped.")

    async def queue_call(self, coro: Callable[[], Awaitable[Any]]) -> Any:
        task_id = str(uuid.uuid4())
        future = asyncio.get_running_loop().create_future()
        self.task_map[task_id] = future

        self.logger.info(f"[Queue] Task {task_id} added to queue.")
        await self.queue.put((task_id, coro))

        # 🚨 Sanity check that worker is running
        if self.worker and self.worker.done():
            error = self.worker.exception()
            self.logger.error(f"[Queue] Worker crashed: {error}")
            raise RuntimeError(f"Worker crashed with: {error}")

        return await future  # Will be completed by worker

    async def _worker_loop(self):
        self.logger.info("[Queue] Worker loop started.")
        while self.running:
            try:
                task_id, coro = await self.queue.get()
                self.logger.info(f"[Queue] Got task {task_id} from queue")

                now = time.time()
                delay = max(0, self.rate_limit - (now - self.last_call_time))
                if delay > 0:
                    self.logger.info(f"[Queue] Waiting {delay:.2f}s due to rate limit.")
                    await asyncio.sleep(delay)

                self.logger.info(f"[Queue] Executing task {task_id}")
                future = self.task_map.pop(task_id, None)

                try:
                    result = await coro()
                    if future and not future.done():
                        future.set_result(result)
                        self.logger.info(f"[Queue] Task {task_id} completed.")
                except Exception as e:
                    self.logger.error(f"[Queue] Task {task_id} failed: {e}")
                    if future and not future.done():
                        future.set_exception(e)

                self.last_call_time = time.time()
                self.queue.task_done()

            except asyncio.CancelledError:
                self.logger.warning("[Queue] Worker cancelled.")
                break
            except Exception as e:
                self.logger.exception(f"[Queue] Unhandled exception in worker loop: {e}")
