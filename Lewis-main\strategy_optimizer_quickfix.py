"""
Quick fix for the original strategy optimizer to make it functional
This addresses the critical issues while maintaining the original structure
"""
import copy
from typing import Dict, <PERSON>, Tu<PERSON>, Any
from strategy import Strategy, TimeFrame
from parameter_config import VALID_STRATEGY_PARAMETERS

# Fix 1: Add missing PARAMETER_RANGES
def get_parameter_ranges(strategy_type: str) -> Dict[str, Tuple[float, float]]:
    """Get parameter ranges for a strategy type"""
    if strategy_type not in VALID_STRATEGY_PARAMETERS:
        return {}
    
    ranges = {}
    config = VALID_STRATEGY_PARAMETERS[strategy_type]
    
    # Flatten nested parameter structure
    for param_group in ['market_parameters', 'calculation_parameters']:
        if param_group in config:
            if param_group == 'market_parameters':
                # Handle nested market parameters
                for subgroup in config[param_group].values():
                    ranges.update(subgroup)
            else:
                # Handle flat calculation parameters
                ranges.update(config[param_group])
    
    return ranges

# Fix 2: Add missing Strategy methods
def clone_strategy(strategy: Strategy) -> Strategy:
    """Clone a strategy (missing method fix)"""
    return copy.deepcopy(strategy)

def get_strategy_parameters(strategy: Strategy) -> Dict[str, Any]:
    """Get strategy parameters (missing method fix)"""
    if hasattr(strategy, 'parameters') and strategy.parameters:
        return strategy.parameters.copy()
    return {}

def apply_parameters_to_strategy(strategy: Strategy, params: Dict[str, Any]) -> Strategy:
    """Apply parameters to a strategy"""
    cloned = clone_strategy(strategy)
    
    if not hasattr(cloned, 'parameters'):
        cloned.parameters = {}
    
    cloned.parameters.update(params)
    return cloned

# Fix 3: Enhanced fitness evaluation with error handling
def safe_fitness_evaluation(strategy: Strategy, market_simulator: Any, n_scenarios: int = 3) -> float:
    """Safely evaluate strategy fitness with error handling"""
    try:
        total_sharpe = 0
        successful_runs = 0
        
        for _ in range(n_scenarios):
            try:
                performance = market_simulator.run_simulation(strategy)
                sharpe = performance.get('sharpe_ratio', 0)
                if not (sharpe != sharpe):  # Check for NaN
                    total_sharpe += sharpe
                    successful_runs += 1
            except Exception as e:
                # Log error but continue
                print(f"Simulation failed: {e}")
                continue
        
        if successful_runs == 0:
            return -1.0  # Poor fitness for failed strategies
        
        return total_sharpe / successful_runs
        
    except Exception as e:
        print(f"Fitness evaluation failed: {e}")
        return -1.0

# Original StrategyOptimizer class with fixes applied
class StrategyOptimizerFixed:
    """Fixed version of the original strategy optimizer"""
    
    def __init__(self, config: Any, market_simulator: Any, strategies: Dict[TimeFrame, Dict[str, Strategy]]):
        self.config = config
        self.market_simulator = market_simulator
        self.strategies = strategies
    
    def optimize_strategy(self, strategy: Strategy, algorithm: str = "genetic") -> Strategy:
        """Optimize a single strategy using the specified algorithm"""
        
        # Fix 1: Get parameter ranges properly
        strategy_type = strategy.favored_patterns[0] if strategy.favored_patterns else "trend_following"
        param_ranges = get_parameter_ranges(strategy_type)
        
        if not param_ranges:
            print(f"No parameter ranges found for strategy type: {strategy_type}")
            return strategy
        
        if algorithm == "genetic":
            return self._genetic_algorithm_fixed(strategy, param_ranges)
        elif algorithm == "differential":
            return self._differential_evolution_fixed(strategy, param_ranges)
        else:
            print(f"Unknown algorithm: {algorithm}")
            return strategy
    
    def _genetic_algorithm_fixed(self, strategy: Strategy, param_ranges: Dict[str, Tuple[float, float]]) -> Strategy:
        """Fixed genetic algorithm implementation"""
        
        param_names = list(param_ranges.keys())
        bounds = [param_ranges[name] for name in param_names]
        
        # Simple GA parameters
        population_size = 20
        generations = 30
        mutation_rate = 0.1
        
        # Initialize population
        population = []
        for _ in range(population_size):
            individual = []
            for (min_val, max_val) in bounds:
                individual.append(min_val + (max_val - min_val) * random.random())
            population.append(individual)
        
        best_strategy = strategy
        best_fitness = safe_fitness_evaluation(strategy, self.market_simulator)
        
        for generation in range(generations):
            # Evaluate population
            fitnesses = []
            for individual in population:
                # Apply parameters to strategy
                params = {name: val for name, val in zip(param_names, individual)}
                test_strategy = apply_parameters_to_strategy(strategy, params)
                
                fitness = safe_fitness_evaluation(test_strategy, self.market_simulator)
                fitnesses.append(fitness)
                
                # Track best
                if fitness > best_fitness:
                    best_fitness = fitness
                    best_strategy = test_strategy
            
            # Simple selection and mutation
            # Keep top 50% and mutate them
            sorted_indices = sorted(range(len(fitnesses)), key=lambda i: fitnesses[i], reverse=True)
            elite_count = population_size // 2
            
            new_population = []
            
            # Keep elite
            for i in range(elite_count):
                idx = sorted_indices[i]
                new_population.append(population[idx][:])
            
            # Mutate elite to fill rest
            for i in range(population_size - elite_count):
                parent_idx = sorted_indices[i % elite_count]
                child = population[parent_idx][:]
                
                # Mutate
                for j in range(len(child)):
                    if random.random() < mutation_rate:
                        min_val, max_val = bounds[j]
                        child[j] = min_val + (max_val - min_val) * random.random()
                
                new_population.append(child)
            
            population = new_population
            
            if generation % 10 == 0:
                print(f"Generation {generation}: Best fitness = {best_fitness:.4f}")
        
        print(f"Optimization complete. Best fitness: {best_fitness:.4f}")
        return best_strategy
    
    def _differential_evolution_fixed(self, strategy: Strategy, param_ranges: Dict[str, Tuple[float, float]]) -> Strategy:
        """Fixed differential evolution implementation"""
        
        try:
            from scipy.optimize import differential_evolution
        except ImportError:
            print("scipy not available, falling back to genetic algorithm")
            return self._genetic_algorithm_fixed(strategy, param_ranges)
        
        param_names = list(param_ranges.keys())
        bounds = [param_ranges[name] for name in param_names]
        
        def objective(params_array):
            params = {name: val for name, val in zip(param_names, params_array)}
            test_strategy = apply_parameters_to_strategy(strategy, params)
            fitness = safe_fitness_evaluation(test_strategy, self.market_simulator)
            return -fitness  # Minimize negative fitness
        
        try:
            result = differential_evolution(
                objective,
                bounds=bounds,
                maxiter=50,
                popsize=15,
                seed=42
            )
            
            # Apply best parameters
            best_params = {name: val for name, val in zip(param_names, result.x)}
            optimized_strategy = apply_parameters_to_strategy(strategy, best_params)
            
            print(f"DE Optimization complete. Best fitness: {-result.fun:.4f}")
            return optimized_strategy
            
        except Exception as e:
            print(f"Differential evolution failed: {e}")
            return self._genetic_algorithm_fixed(strategy, param_ranges)
    
    def optimize_all_strategies(self, algorithm: str = "genetic") -> Dict[TimeFrame, List[Strategy]]:
        """Optimize all strategies"""
        
        optimized_strategies = {}
        
        for timeframe in TimeFrame:
            if timeframe not in self.strategies:
                optimized_strategies[timeframe] = []
                continue
            
            timeframe_strategies = []
            
            for strategy_name, strategy in self.strategies[timeframe].items():
                print(f"Optimizing {strategy_name} for {timeframe.value}...")
                
                try:
                    optimized = self.optimize_strategy(strategy, algorithm)
                    timeframe_strategies.append(optimized)
                except Exception as e:
                    print(f"Failed to optimize {strategy_name}: {e}")
                    timeframe_strategies.append(strategy)  # Keep original
            
            optimized_strategies[timeframe] = timeframe_strategies
        
        return optimized_strategies

# Add missing import
import random
