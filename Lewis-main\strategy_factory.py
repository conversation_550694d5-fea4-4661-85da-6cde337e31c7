import json
import asyncio
from typing import Dict, Callable
import pandas as pd
import aiofiles
import re
import uuid
from strategy import Strategy, TimeFrame
from config import Config
import hashlib

class StrategyFactory:
    def __init__(self, config=Config, config_file_path='strategies.json'):
        self.config = config
        self.strategies = {}
        self.signal_methods = {}
        self.config_file_path = config_file_path
        self.load_strategy_config()
        asyncio.create_task(self.monitor_strategies())
    # add this at class-level
    _TF_MAP = {
        'h':       'short_term',
        '4h':         'mid_term',
        'D':        'long_term',
        'W':         'seasonal_term',
    }

    def load_strategy_config(self):
        try:
            with open(self.config_file_path, 'r') as f:
                content = f.read().strip()
                if not content:
                    return {}

                parsed = json.loads(content)

                # If correct format
                if isinstance(parsed, dict):
                    return parsed

                # If format is list, auto-convert and retry
                elif isinstance(parsed, list):
                    print("⚠️ Strategy config is in list format. Converting to dict...")
                    converted = self.convert_strategy_config("dict")

                    # Save converted dict back to file
                    with open(self.config_file_path, 'w') as f2:
                        json.dump(converted, f2, indent=4)

                    return converted

                else:
                    print(f"⚠️ Invalid config format: Expected dict or list but got {type(parsed).__name__}")
                    return {}

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"⚠️ Error loading strategy config from {self.config_file_path}: {e}")
            return {}

    async def update_strategy(self, strategy_name: str, strategy: Strategy):
        strategy_key = f"{strategy_name}_{strategy.time_frame}_{hash(frozenset(strategy.parameters.items()))}"

        # Store in memory
        self.strategies[strategy_key] = strategy
        self.signal_methods[strategy_key] = self.create_signal_method(strategy)

        # Load current strategies
        try:
            async with aiofiles.open(self.config_file_path, 'r') as f:
                content = await f.read()
                existing_strategies = json.loads(content) if content else {}
        except FileNotFoundError:
            existing_strategies = {}

        vol_min, vol_max = strategy.optimal_volatility

        # Add or update strategy
        existing_strategies[strategy_key] = {
            'name': strategy.name,
            'description': strategy.description,  # ✅ Add this line
            'time_frame': strategy.time_frame.value,
            'parameters': dict(strategy.parameters),
            'favored_patterns': list(strategy.favored_patterns),
            'market_conditions': {
                'optimal_volatility': {'min': vol_min, 'max': vol_max},
                'optimal_trend': strategy.optimal_trend,
                'optimal_liquidity': strategy.optimal_liquidity
            }
        }


        # Save all strategies
        async with aiofiles.open(self.config_file_path, 'w') as f:
            await f.write(json.dumps(existing_strategies, indent=4))

    def create_signal_method(self, strategy: Strategy) -> Callable:
        def signal_method(market_data: pd.DataFrame) -> float:
            return strategy.generate_signal(market_data)
        return signal_method

    def get_signal_method(self, strategy_name: str) -> Callable:
        return self.signal_methods.get(strategy_name, self.default_signal)

    @staticmethod
    def default_signal(market_data: pd.DataFrame) -> float:
        return 0

    async def monitor_strategies(self):
        while True:
            await asyncio.sleep(60)  # Check every minute
            current_strategies = set(self.strategies.keys())
            loaded_config = self.load_strategy_config()
            config_strategies = set(loaded_config.keys()) if loaded_config else set()

            if self.strategies and current_strategies != config_strategies:
                await self.save_all_strategies()
                print("Strategy configuration updated.")

    def delete_strategy(self, strategy_name: str):
        if strategy_name in self.strategies:
            del self.strategies[strategy_name]
            del self.signal_methods[strategy_name]
            asyncio.create_task(self.save_all_strategies())
            print(f"Strategy {strategy_name} deleted.")

    def create_strategy(self, strategy_config: Dict) -> Strategy:
        # first normalize any inconsistent keys…
        cfg = self.normalize_strategy_entry(strategy_config)

        # --- Flatten nested parameters if needed ---
        raw_params = cfg.get('parameters', {})
        if isinstance(raw_params, dict) and 'market_parameters' in raw_params:
            flat = {}
            flat.update(raw_params.get('calculation_parameters', {}))
            mp = raw_params['market_parameters']
            for cat in ['signal_strengths', 'directional_biases', 'action_sensitivities']:
                flat.update(mp.get(cat, {}))
        else:
            flat = raw_params
        # use flat dict for Strategy storage
        parameters = flat

        market = cfg.get('market_conditions', {})

        vol = market.get('optimal_volatility', "0.0-1.0")
        # split string "min-max" back to floats
        vmin, vmax = [float(x) for x in vol.split('-')]
        trend = market.get('optimal_trend', 'range')
        liq_str = market.get('optimal_liquidity', "1.0+")
        liq = float(liq_str.rstrip('+'))

        return Strategy(
            name=cfg['name'],
            description=cfg['description'],
            parameters=parameters,
            favored_patterns=list(cfg.get('patterns', [])),
            time_frame=TimeFrame(cfg['timeframe']),
            optimal_volatility=(vmin, vmax),
            optimal_trend=trend,
            optimal_liquidity=liq
        )

    def _get_timeframe_parameters(self, timeframe: TimeFrame):
        base_params = self.config.get_base_params()
        timeframe_specific = {
            TimeFrame.SHORT_TERM: {'interval': 'min', 'lookback': 60},
            TimeFrame.MID_TERM: {'interval': 'h', 'lookback': 504},
            TimeFrame.LONG_TERM: {'interval': 'D', 'lookback': 365},
            TimeFrame.SEASONAL_TERM: {'interval': 'ME', 'lookback': 1460}
        }
        return {**base_params, **timeframe_specific[timeframe]}

    async def save_all_strategies(self):
        serializable_strategies = {
            key: {
                'name': strat.name,
                'description': strat.description,  # ✅ Add this line
                'time_frame': strat.time_frame.value,
                'parameters': dict(strat.parameters),
                'favored_patterns': list(strat.favored_patterns),
                'market_conditions': {
                    'optimal_volatility': {
                        'min': strat.optimal_volatility[0],
                        'max': strat.optimal_volatility[1]
                    },
                    'optimal_trend': strat.optimal_trend,
                    'optimal_liquidity': strat.optimal_liquidity
                }
            }
            for key, strat in self.strategies.items()
        }

        async with aiofiles.open(self.config_file_path, 'w') as f:
            await f.write(json.dumps(serializable_strategies, indent=4))

    def get_existing_strategy_descriptions(self) -> str:
        """
        Return a string of all current strategies in the format:
        strategy_name: description
        """
        try:
            with open(self.config_file_path, 'r') as f:
                content = f.read().strip()
                existing = json.loads(content) if content else {}
        except FileNotFoundError:
            existing = {}

        return "\n".join(
            f"{entry['name']}: {entry.get('description', 'No description')}"
            for entry in existing.values()
        )
    
    def convert_strategy_config(self, target_format: str) -> dict | list:
        """
        Converts the strategy config between list and dict formats.
        target_format: either 'dict' or 'list'
        """
        try:
            with open(self.config_file_path, 'r') as f:
                content = f.read().strip()
                if not content:
                    return {} if target_format == 'dict' else []
                current_data = json.loads(content)
        except (FileNotFoundError, json.JSONDecodeError):
            return {} if target_format == 'dict' else []

        # Convert list -> dict
        if isinstance(current_data, list) and target_format == 'dict':
            converted = {}
            for entry in current_data:
                # Use a deterministic key if possible, fallback to uuid
                key_base = entry.get("name", "strategy").replace(" ", "_").lower()
                key = f"{key_base}_{uuid.uuid4().hex[:6]}"
                converted[key] = entry
            return converted

        # Convert dict -> list
        if isinstance(current_data, dict) and target_format == 'list':
            return list(current_data.values())

        # Already correct format
        return current_data

    def add_manual_strategy(self, strategy_data: dict):
        """
        Adds a manually defined strategy to the strategy config.
        Ensures uniqueness and proper formatting.
        """
        # Load and ensure dict format
        config = self.load_strategy_config()
        if not isinstance(config, dict):
            config = self.convert_strategy_config("dict")
            with open(self.config_file_path, 'w') as f:
                json.dump(config, f, indent=4)

        # Generate a unique key from name + timeframe + hash of params
        name = strategy_data["name"]
        tf = strategy_data["timeframe"]
        params_str = json.dumps(strategy_data["parameters"], sort_keys=True)
        uid = hashlib.md5(params_str.encode()).hexdigest()[:8]
        strategy_key = f"{name}_{tf}_{uid}"

        # Normalize format
        entry = {
            "name": strategy_data["name"],
            "description": strategy_data["description"],
            "time_frame": tf,
            "parameters": strategy_data["parameters"],
            "favored_patterns": strategy_data["patterns"],
            "market_conditions": {
                "optimal_volatility": {
                    "min": float(strategy_data["market_conditions"]["optimal_volatility"].split("-")[0]),
                    "max": float(strategy_data["market_conditions"]["optimal_volatility"].split("-")[-1])
                },
                "optimal_trend": strategy_data["market_conditions"]["optimal_trend"],
                "optimal_liquidity": float(strategy_data["market_conditions"]["optimal_liquidity"].replace("+", "").replace("≥", ""))
            }
        }

        config[strategy_key] = entry

        # Save updated config
        with open(self.config_file_path, 'w') as f:
            json.dump(config, f, indent=4)

        print(f"✅ Manually added strategy '{name}' as '{strategy_key}'.")

    @staticmethod
    def normalize_strategy_entry(entry: dict) -> dict:
        """
        1) Unify keys:
           - time_frame / timeFrame / timeframe → 'timeframe'
           - favored_patterns → 'patterns'
        2) Map shorthand intervals → full timeframe names
        3) Flatten market_conditions:
           • optimal_volatility dict → "min-max" string
           • optimal_liquidity float → "x+" string
        """
        e = entry.copy()

        # ——— 1) unify timeframe key ———
        for k in ('time_frame', 'timeFrame', 'timeframe'):
            if k in e:
                raw_tf = e.pop(k)
                break
        else:
            raw_tf = None

        # map shorthand → canonical
        tf = StrategyFactory._TF_MAP.get(raw_tf, raw_tf)
        if tf:
            e['timeframe'] = tf

        # ——— unify pattern list ———
        if 'favored_patterns' in e:
            e['patterns'] = e.pop('favored_patterns')

        # ——— 2) normalize market_conditions ———
        mc = e.get('market_conditions', {}).copy()
        # volatility
        vol = mc.get('optimal_volatility')
        if isinstance(vol, dict) and 'min' in vol and 'max' in vol:
            mc['optimal_volatility'] = f"{vol['min']}-{vol['max']}"
        # liquidity
        liq = mc.get('optimal_liquidity')
        if isinstance(liq, (int, float)):
            mc['optimal_liquidity'] = f"{liq}+"

        e['market_conditions'] = mc
        return e


