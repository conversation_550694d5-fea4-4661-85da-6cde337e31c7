import _frozen_importlib # frozen
import _imp # builtin
import '_thread' # <class '_frozen_importlib.BuiltinImporter'>
import '_warnings' # <class '_frozen_importlib.BuiltinImporter'>
import '_weakref' # <class '_frozen_importlib.BuiltinImporter'>
import '_io' # <class '_frozen_importlib.BuiltinImporter'>
import 'marshal' # <class '_frozen_importlib.BuiltinImporter'>
import 'posix' # <class '_frozen_importlib.BuiltinImporter'>
import '_frozen_importlib_external' # <class '_frozen_importlib.FrozenImporter'>
# installing zipimport hook
import 'time' # <class '_frozen_importlib.BuiltinImporter'>
import 'zipimport' # <class '_frozen_importlib.FrozenImporter'>
# installed zipimport hook
import 'faulthandler' # <class '_frozen_importlib.BuiltinImporter'>
# /usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/encodings/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/__init__.cpython-312.pyc'
import '_codecs' # <class '_frozen_importlib.BuiltinImporter'>
import 'codecs' # <class '_frozen_importlib.FrozenImporter'>
# /usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/aliases.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/encodings/aliases.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/aliases.cpython-312.pyc'
import 'encodings.aliases' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba240c6a0>
import 'encodings' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba23fc2e0>
# /usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/utf_8.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/encodings/utf_8.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/utf_8.cpython-312.pyc'
import 'encodings.utf_8' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba2414470>
import '_signal' # <class '_frozen_importlib.BuiltinImporter'>
import '_abc' # <class '_frozen_importlib.BuiltinImporter'>
import 'abc' # <class '_frozen_importlib.FrozenImporter'>
import 'io' # <class '_frozen_importlib.FrozenImporter'>
# /usr/local/python/3.12.1/lib/python3.12/__pycache__/warnings.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/warnings.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/__pycache__/warnings.cpython-312.pyc'
import 'warnings' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba24173a0>
import '_stat' # <class '_frozen_importlib.BuiltinImporter'>
import 'stat' # <class '_frozen_importlib.FrozenImporter'>
import '_collections_abc' # <class '_frozen_importlib.FrozenImporter'>
import 'genericpath' # <class '_frozen_importlib.FrozenImporter'>
import 'posixpath' # <class '_frozen_importlib.FrozenImporter'>
import 'os' # <class '_frozen_importlib.FrozenImporter'>
import '_sitebuiltins' # <class '_frozen_importlib.FrozenImporter'>
Processing user site-packages
Adding directory: '/home/<USER>/.local/lib/python3.12/site-packages'
Processing .pth file: '/home/<USER>/.local/lib/python3.12/site-packages/distutils-precedence.pth'
# /home/<USER>/.local/lib/python3.12/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/_distutils_hack/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc'
import '_distutils_hack' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba243ff30>
Processing global site-packages
Adding directory: '/usr/local/python/3.12.1/lib/python3.12/site-packages'
Processing .pth file: '/usr/local/python/3.12.1/lib/python3.12/site-packages/google_generativeai-0.8.3-py3.12-nspkg.pth'
# /usr/local/python/3.12.1/lib/python3.12/__pycache__/types.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/types.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/__pycache__/types.cpython-312.pyc'
import 'types' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba2453a80>
# /usr/local/python/3.12.1/lib/python3.12/importlib/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/importlib/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/importlib/__pycache__/__init__.cpython-312.pyc'
import 'importlib' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba2451960>
# /usr/local/python/3.12.1/lib/python3.12/importlib/__pycache__/_abc.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/importlib/_abc.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/importlib/__pycache__/_abc.cpython-312.pyc'
import 'importlib._abc' # <_frozen_importlib_external.SourceFileLoader object at 0x7c5ba2462810>
import 'importlib.util' # <class '_frozen_importlib.FrozenImporter'>
import 'importlib.machinery' # <class '_frozen_importlib.FrozenImporter'>
# possible namespace for /usr/local/python/3.12.1/lib/python3.12/site-packages/google
import 'site' # <class '_frozen_importlib.FrozenImporter'>
Python 3.12.1 (main, Sep 30 2024, 17:05:21) [GCC 9.4.0] on linux
Type "help", "copyright", "credits" or "license" for more information.
  File "/workspaces/Trading-Bot2/main.py", line 67
    logger.error(f"Startup error: {str(e)}", exc_info=True)    asyncio.run(main())
                                                               ^^^^^^^
SyntaxError: invalid syntax
# clear sys.path_importer_cache
# clear sys.path_hooks
# clear builtins._
# clear sys.path
# clear sys.argv
# clear sys.ps1
# clear sys.ps2
# clear sys.last_exc
# clear sys.last_type
# clear sys.last_value
# clear sys.last_traceback
# clear sys.__interactivehook__
# clear sys.meta_path
# restore sys.stdin
# restore sys.stdout
# restore sys.stderr
# cleanup[2] removing sys
# cleanup[2] removing builtins
# cleanup[2] removing _frozen_importlib
# cleanup[2] removing _imp
# cleanup[2] removing _thread
# cleanup[2] removing _warnings
# cleanup[2] removing _weakref
# cleanup[2] removing _io
# cleanup[2] removing marshal
# cleanup[2] removing posix
# cleanup[2] removing _frozen_importlib_external
# cleanup[2] removing time
# cleanup[2] removing zipimport
# destroy zipimport
# cleanup[2] removing faulthandler
# cleanup[2] removing _codecs
# cleanup[2] removing codecs
# cleanup[2] removing encodings.aliases
# cleanup[2] removing encodings
# destroy encodings
# cleanup[2] removing encodings.utf_8
# cleanup[2] removing _signal
# cleanup[2] removing _abc
# cleanup[2] removing abc
# cleanup[2] removing io
# cleanup[2] removing __main__
# destroy __main__
# cleanup[2] removing warnings
# cleanup[2] removing _stat
# cleanup[2] removing stat
# cleanup[2] removing _collections_abc
# destroy _collections_abc
# cleanup[2] removing genericpath
# cleanup[2] removing posixpath
# cleanup[2] removing os.path
# cleanup[2] removing os
# cleanup[2] removing _sitebuiltins
# cleanup[2] removing _distutils_hack
# destroy _distutils_hack
# cleanup[2] removing types
# cleanup[2] removing importlib._bootstrap
# cleanup[2] removing importlib._bootstrap_external
# cleanup[2] removing importlib
# destroy importlib
# cleanup[2] removing importlib._abc
# cleanup[2] removing importlib.util
# cleanup[2] removing importlib.machinery
# cleanup[2] removing google
# destroy google
# cleanup[2] removing site
# destroy site
# destroy time
# destroy faulthandler
# destroy _signal
# destroy _abc
# destroy _sitebuiltins
# destroy io
# destroy posixpath
# destroy _stat
# destroy genericpath
# destroy stat
# destroy os
# destroy warnings
# destroy importlib._abc
# destroy importlib.util
# destroy importlib.machinery
# destroy abc
# destroy types
# cleanup[3] wiping importlib._bootstrap_external
# cleanup[3] wiping importlib._bootstrap
# destroy _frozen_importlib_external
# cleanup[3] wiping encodings.utf_8
# cleanup[3] wiping encodings.aliases
# cleanup[3] wiping codecs
# cleanup[3] wiping _codecs
# cleanup[3] wiping posix
# cleanup[3] wiping marshal
# destroy marshal
# cleanup[3] wiping _io
# cleanup[3] wiping _weakref
# destroy _weakref
# cleanup[3] wiping _warnings
# destroy _warnings
# cleanup[3] wiping _thread
# cleanup[3] wiping _imp
# destroy _imp
# cleanup[3] wiping _frozen_importlib
# cleanup[3] wiping sys
# cleanup[3] wiping builtins
# destroy sys.monitoring
# destroy _thread
# destroy _io
# destroy posix
# clear sys.meta_path
# clear sys.modules
# destroy _frozen_importlib
# destroy codecs
# destroy sys
# destroy encodings.aliases
# destroy encodings.utf_8
# destroy _codecs
# destroy builtins
# clear sys.audit hooks
