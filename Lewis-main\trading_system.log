*Testing Phase:
Run strategies through historical data
Monitor market conditions and strategy performance
Dynamically create new strategies when market gaps identified
Track parameter impact on trading decisions

Optimization Phase:
Analyze failed trades and scenarios
Adjust parameters based on performance data
Optimize both selected and non-selected strategies
Fine-tune parameters for specific market conditions

Parameter Impact Analysis:
Track how parameter changes affect:
Entry/exit timing
Position sizing
Risk management
Overall returns

Strategy gap issue:
use if backtesing is running or if you are selecting a short term strategy for live trading you have a different ai model check if 
there is a market missmatch between the selected strategy and the market.
if not tell it to respond with no and set the strategy if yes then make everything else a list of issues to then send as additional
propmt data for the strategy generator method create targeted strategy. Then set it as the current one. Make sure the the seletor
gets the rest of the market data as well to create the new strategy.

AI NFT and memecoin:
Make sure it looks at the most popular and expensive one and see if there is a certain sentiment to it like most of the popular ones
have pandas or such. Make sure it is connecting to a memecoin aggregator for large amoutns of data to find sentiment
Also if certain people or other things are mentioned in the description or title as that could also be a sign of a good one.
Connect it to twitter, reddit and other social media to see what people are saying about it and what they think about it.
Also use the socials to find certain sentiments about certain things and use that to see what is currently popular and what is not.
Also use it to find certain changes in certain memcoins like it partners with something or someone as that could cause a rise in price.


Future Code Optimization:
 API call management:
  1. Queue Monitoring Dashboard
  2. Submission Efficiency (Batching)
  3. Error Handling (Retries)
  4. Multi-Rate Buckets (Multiple API Keys)
  5. Memory Optimization for Giant Jobs
  6. auto-calculate min_interval
 Market Simulator:
  1. pre-compute a lookup table of quantiles with SciPy and then interpolate inside a Numba-jitted function for _generate_correlated_factors.
  2. Make the strategy simulator a Global simulator which basically means there will only be one instance and the entire system will train off
     the daata it constantly produces. This will also be neccesary for the main AI system. (Also will save insane amounts of computational resources)
     Finally to keep diversity in the system 
  3.
 Genetic Signal Generator:
  1. Deep multi-param interaction modeling
  2. Trade-aware evaluation
  3. hybrid logic gate and genetic evaluater
  4. Introduce an interpreter module
  5. Give every strategy it's own rule to constantly refine
  6. Create an LLM to replace
  7. Implement the diversity weighting directly in evaluate_fitness
  8. auto-restore from the last saved expression if an error occurs mid-run
 Strategy generation:
  1. Add statistical arbitrage back to the generation and system.

Early AI integration:
Currently bulding an HRL AI to control the data for each AI agent in training and also manage the AI training. Once ready I will integrate
the HRL and begin it's training then after a week or so I will start integrating AI to train not use yet. Then once the AI has trained enough
I will actually start using the AI.

Current tasks:

1. complete the Genetic Signal Generator
2. start evolution cycle by creating random strategies and letting it figure out correlations and include the evolution of this in the optimizer.
3. implement Genetic Signal Generator in the Backtester and market simulator
4. finish debugging strategy optimizer
5. add pattern favoring to the backtester strategy selection (Different patterns are more likely to get chosen in certain regimes)
6. Improve the backtester strategy selection system
7. debug market maker
