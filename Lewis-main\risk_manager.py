from config import Config
import ccxt
import ta
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from moralis import evm_api
import san
from strategy import Strategy
from typing import Dict, Tuple, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

class RiskLevel(Enum):
    """Risk level classifications"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    EXTREME = "extreme"

@dataclass
class RiskMetrics:
    """Container for risk assessment metrics"""
    volatility_risk: float
    liquidity_risk: float
    trend_risk: float
    sentiment_risk: float
    on_chain_risk: float
    funding_risk: float
    correlation_risk: float
    drawdown_risk: float
    overall_risk_score: float
    risk_level: RiskLevel
    recommended_position_multiplier: float

@dataclass
class PositionSizing:
    """Container for position sizing calculations"""
    base_position_size: float
    risk_adjusted_size: float
    max_allowed_size: float
    final_position_size: float
    stop_loss_price: float
    take_profit_price: float
    risk_reward_ratio: float

class RiskManager:
    def __init__(self, config):
        self.config = config

        # Initialize APIs
        self.moralis_api_key = config.BASE_PARAMS['MORALIS_API_KEY']
        self.exchange = ccxt.kraken({
            'apiKey': config.BASE_PARAMS['KRAKEN_API_KEY'],
            'secret': config.BASE_PARAMS['KRAKEN_PRIVATE_KEY']
        })

        # Risk parameters
        self.max_position_size = config.ADAPTIVE_PARAMS['MAX_POSITION_SIZE']
        self.base_stop_loss_pct = config.BASE_PARAMS['BASE_STOP_LOSS_PCT']
        self.base_take_profit_pct = config.BASE_PARAMS['BASE_TAKE_PROFIT_PCT']
        self.max_drawdown = config.ADAPTIVE_PARAMS['MAX_DRAWDOWN']
        self.volatility_window = config.ADAPTIVE_PARAMS['VOLATILITY_WINDOW']
        self.liquidity_threshold = config.ADAPTIVE_PARAMS['LIQUIDITY_THRESHOLD']

    def fetch_order_book(self, symbol='BTC/USDT', limit=20):
        return self.exchange.fetch_order_book(symbol, limit)

    def check_liquidity(self, order_book):
        bid_liquidity = sum(bid[1] for bid in order_book['bids'][:10])
        ask_liquidity = sum(ask[1] for ask in order_book['asks'][:10])
        return min(bid_liquidity, ask_liquidity) > self.liquidity_threshold

    def calculate_position_size(self, portfolio_value, entry_price, stop_loss_price):
        risk_per_trade = portfolio_value * self.config.ADAPTIVE_PARAMS['RISK_PER_TRADE']
        price_risk = abs(entry_price - stop_loss_price)
        position_size = risk_per_trade / price_risk
        max_allowed = portfolio_value * self.max_position_size
        return min(position_size, max_allowed)

    def set_dynamic_stop_loss(self, entry_price, position_type, atr):
        atr_multiplier = 2  # Adjust based on risk tolerance
        if position_type == "long":
            return entry_price - (atr * atr_multiplier)
        elif position_type == "short":
            return entry_price + (atr * atr_multiplier)
        else:
            raise ValueError("Invalid position type. Must be 'long' or 'short'.")

    def set_dynamic_take_profit(self, entry_price, position_type, atr):
        atr_multiplier = 3  # Adjust based on desired risk-reward ratio
        if position_type == "long":
            return entry_price + (atr * atr_multiplier)
        elif position_type == "short":
            return entry_price - (atr * atr_multiplier)
        else:
            raise ValueError("Invalid position type. Must be 'long' or 'short'.")

    def calculate_atr(self, historical_data, period=14):
        high = historical_data['high']
        low = historical_data['low']
        close = historical_data['close']

        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()

        return atr.iloc[-1]

    def check_extreme_volatility(self, historical_data):
        returns = historical_data['close'].pct_change()
        volatility = returns.std() * np.sqrt(365)  # Annualized volatility
        return volatility > self.config.VOLATILITY_THRESHOLD

    def check_max_drawdown(self, current_value, peak_value):
        drawdown = (peak_value - current_value) / peak_value
        return drawdown > self.max_drawdown

    def get_market_sentiment(self):
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        try:
            social_volume = san.get(
                "social_volume_total",
                slug="bitcoin",
                from_date=start_date,
                to_date=end_date,
                interval="1d"
            )

            sentiment_balance = san.get(
                "sentiment_balance_total",
                slug="bitcoin",
                from_date=start_date,
                to_date=end_date,
                interval="1d"
            )

            social_volume_score = self.normalize_data(social_volume['value'])
            sentiment_balance_score = self.normalize_data(sentiment_balance['value'])

            composite_score = (social_volume_score * 0.4 + sentiment_balance_score * 0.6)
            return int(composite_score * 100)  # Return score on a 0-100 scale

        except Exception as e:
            print(f"Error fetching sentiment data: {e}")
            return 50  # Return neutral sentiment if there's an error

    def get_on_chain_metrics(self):
        try:
            # Using Moralis to get on-chain metrics
            params = {
                "chain": "eth",
                "address": self.config.CONTRACT_ADDRESS,
            }
            result = evm_api.token.get_token_price(
                api_key=self.moralis_api_key,
                params=params,
            )

            # Extract relevant metrics from Moralis response
            token_price = result.get('usdPrice', 0)
            token_volume = result.get('24hrVolume', 0)

            # Normalize the data
            price_score = self.normalize_data([token_price])
            volume_score = self.normalize_data([token_volume])

            composite_score = (price_score * 0.5 + volume_score * 0.5)
            return composite_score[0]
        except Exception as e:
            print(f"Error fetching on-chain metrics: {e}")
            return 0.5  # Return neutral score if there's an error

    def normalize_data(self, data):
        return (data - np.min(data)) / (np.max(data) - np.min(data))

    def get_funding_rate(self):
        try:
            funding_rate = self.exchange.fetch_funding_rate('XBT/USDT')
            return funding_rate['fundingRate']
        except:
            return 0  # Return 0 if unable to fetch funding rate

    def apply_risk_management(self, signal, portfolio_value, current_price, historical_data, strategy: Strategy):
        # Initial liquidity and volatility checks
        order_book = self.fetch_order_book()
        if not self.check_liquidity(order_book):
            return 0, None, None

        if self.check_extreme_volatility(historical_data):
            return 0, None, None

        position_type = "long" if signal > 0 else "short" if signal < 0 else None
        if position_type is None:
            return 0, None, None

        # Market condition analysis
        volatility = self.calculate_volatility(historical_data)
        trend_strength = self.calculate_trend_strength(historical_data)
        market_efficiency = self.calculate_market_efficiency(historical_data)
    
        # Market alignment scoring
        volatility_match = self._match_volatility_condition(strategy.optimal_volatility, volatility)
        trend_match = self._match_trend_condition(strategy.optimal_trend, trend_strength)
        liquidity_match = self._match_liquidity_condition(strategy.optimal_liquidity, market_efficiency)
        market_alignment_score = (volatility_match * 0.4 + trend_match * 0.3 + liquidity_match * 0.3)

        # Base position calculation
        atr = self.calculate_atr(historical_data)
        stop_loss = self.set_dynamic_stop_loss(current_price, position_type, atr)
        take_profit = self.set_dynamic_take_profit(current_price, position_type, atr)
        base_position_size = self.calculate_position_size(portfolio_value, current_price, stop_loss)

        # On-chain metrics analysis
        on_chain_data = self.get_on_chain_metrics()
        on_chain_multiplier = 1.0
        if on_chain_data['token_price_momentum'] > 0.7:
            on_chain_multiplier = 1.2 if position_type == "long" else 0.8
        elif on_chain_data['token_price_momentum'] < 0.3:
            on_chain_multiplier = 0.8 if position_type == "long" else 1.2

        # Funding rate analysis
        funding_rate = self.get_funding_rate()
        funding_multiplier = 1.0
        if abs(funding_rate) > self.config.FUNDING_RATE_THRESHOLD:
            if (position_type == "long" and funding_rate > 0) or (position_type == "short" and funding_rate < 0):
                funding_multiplier = 0.8
            else:
                funding_multiplier = 1.2

        # Market sentiment integration
        sentiment = self.get_market_sentiment()
        sentiment_multiplier = 1.0 + ((sentiment - 50) / 100)

        # Combine all risk factors
        risk_multiplier = (
            market_alignment_score * 0.3 +
            on_chain_multiplier * 0.25 +
            funding_multiplier * 0.25 +
            sentiment_multiplier * 0.2
        )

        # Apply dynamic position sizing
        final_position_size = base_position_size * risk_multiplier

        # Apply maximum position constraints
        max_allowed = portfolio_value * self.max_position_size
        final_position_size = min(final_position_size, max_allowed)

        return final_position_size, stop_loss, take_profit
    
    def apply_backtest_risk_management(self, signal, portfolio_value, current_price, historical_data, strategy: Strategy):
        position_type = "long" if signal > 0 else "short"
        atr = self.calculate_atr(historical_data)

        stop_loss = self.set_dynamic_stop_loss(current_price, position_type, atr)
        take_profit = self.set_dynamic_take_profit(current_price, position_type, atr)
        base_position_size = self.calculate_position_size(portfolio_value, current_price, stop_loss)

        # Optional: Add strategy-specific multipliers here if needed
        return base_position_size, stop_loss, take_profit

    def assess_comprehensive_risk(self, strategy: Strategy, market_data: pd.DataFrame,
                                current_price: float, portfolio_value: float) -> RiskMetrics:
        """
        Perform comprehensive risk assessment across multiple dimensions.

        Args:
            strategy: Strategy being evaluated
            market_data: Historical market data
            current_price: Current asset price
            portfolio_value: Current portfolio value

        Returns:
            RiskMetrics: Comprehensive risk assessment
        """

        # Calculate individual risk components
        volatility_risk = self._assess_volatility_risk(market_data, strategy)
        liquidity_risk = self._assess_liquidity_risk(current_price)
        trend_risk = self._assess_trend_risk(market_data, strategy)
        sentiment_risk = self._assess_sentiment_risk()
        on_chain_risk = self._assess_on_chain_risk()
        funding_risk = self._assess_funding_risk()
        correlation_risk = self._assess_correlation_risk(market_data)
        drawdown_risk = self._assess_drawdown_risk(portfolio_value)

        # Calculate weighted overall risk score
        risk_weights = {
            'volatility': 0.20,
            'liquidity': 0.15,
            'trend': 0.15,
            'sentiment': 0.10,
            'on_chain': 0.10,
            'funding': 0.10,
            'correlation': 0.10,
            'drawdown': 0.10
        }

        overall_risk_score = (
            volatility_risk * risk_weights['volatility'] +
            liquidity_risk * risk_weights['liquidity'] +
            trend_risk * risk_weights['trend'] +
            sentiment_risk * risk_weights['sentiment'] +
            on_chain_risk * risk_weights['on_chain'] +
            funding_risk * risk_weights['funding'] +
            correlation_risk * risk_weights['correlation'] +
            drawdown_risk * risk_weights['drawdown']
        )

        # Determine risk level and position multiplier
        risk_level, position_multiplier = self._classify_risk_level(overall_risk_score)

        return RiskMetrics(
            volatility_risk=volatility_risk,
            liquidity_risk=liquidity_risk,
            trend_risk=trend_risk,
            sentiment_risk=sentiment_risk,
            on_chain_risk=on_chain_risk,
            funding_risk=funding_risk,
            correlation_risk=correlation_risk,
            drawdown_risk=drawdown_risk,
            overall_risk_score=overall_risk_score,
            risk_level=risk_level,
            recommended_position_multiplier=position_multiplier
        )

    def calculate_optimal_position_sizing(self, strategy: Strategy, signal: float,
                                        market_data: pd.DataFrame, current_price: float,
                                        portfolio_value: float, risk_metrics: RiskMetrics) -> PositionSizing:
        """
        Calculate optimal position sizing based on strategy parameters and risk assessment.

        Args:
            strategy: Strategy generating the signal
            signal: Trading signal strength (-1 to 1)
            market_data: Historical market data
            current_price: Current asset price
            portfolio_value: Current portfolio value
            risk_metrics: Risk assessment results

        Returns:
            PositionSizing: Comprehensive position sizing calculation
        """

        # Extract strategy-specific risk parameters
        strategy_params = strategy.parameters
        risk_aversion = self._get_strategy_risk_aversion(strategy_params)
        position_aggressiveness = self._get_strategy_position_aggressiveness(strategy_params)

        # Calculate ATR for dynamic stops
        atr = self.calculate_atr(market_data)

        # Determine position direction
        position_type = "long" if signal > 0 else "short" if signal < 0 else "neutral"

        if position_type == "neutral":
            return self._create_neutral_position()

        # Calculate stop loss and take profit levels
        stop_loss_price = self._calculate_dynamic_stop_loss(
            current_price, position_type, atr, risk_aversion
        )
        take_profit_price = self._calculate_dynamic_take_profit(
            current_price, position_type, atr, position_aggressiveness
        )

        # Calculate base position size using Kelly Criterion approach
        win_rate = self._estimate_strategy_win_rate(strategy)
        avg_win_loss_ratio = self._estimate_win_loss_ratio(current_price, stop_loss_price, take_profit_price)

        kelly_fraction = self._calculate_kelly_fraction(win_rate, avg_win_loss_ratio)

        # Base position size calculation
        risk_amount = portfolio_value * self.config.ADAPTIVE_PARAMS.get('RISK_PER_TRADE', 0.02) * kelly_fraction
        price_risk = abs(current_price - stop_loss_price) / current_price
        base_position_size = risk_amount / (current_price * price_risk)

        # Apply strategy-specific adjustments
        strategy_multiplier = self._calculate_strategy_multiplier(strategy, market_data, current_price)

        # Apply risk-based adjustments
        risk_adjusted_size = base_position_size * strategy_multiplier * risk_metrics.recommended_position_multiplier

        # Apply signal strength scaling
        signal_adjusted_size = risk_adjusted_size * abs(signal)

        # Apply maximum position constraints
        max_allowed_size = portfolio_value * self.max_position_size
        final_position_size = min(signal_adjusted_size, max_allowed_size)

        # Calculate risk-reward ratio
        risk_reward_ratio = self._calculate_risk_reward_ratio(
            current_price, stop_loss_price, take_profit_price, position_type
        )

        return PositionSizing(
            base_position_size=base_position_size,
            risk_adjusted_size=risk_adjusted_size,
            max_allowed_size=max_allowed_size,
            final_position_size=final_position_size,
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            risk_reward_ratio=risk_reward_ratio
        )

    # === Risk Assessment Helper Methods ===

    def _assess_volatility_risk(self, market_data: pd.DataFrame, strategy: Strategy) -> float:
        """Assess volatility risk based on current market conditions and strategy preferences"""
        try:
            # Calculate current volatility
            returns = market_data['close'].pct_change().dropna()
            current_volatility = returns.rolling(window=self.volatility_window).std().iloc[-1] * np.sqrt(365)

            # Get strategy's optimal volatility range
            optimal_vol_str = strategy.optimal_volatility
            if isinstance(optimal_vol_str, tuple):
                vol_min, vol_max = optimal_vol_str
            else:
                # Parse string format like "0.01-0.05"
                vol_parts = str(optimal_vol_str).split('-')
                vol_min = float(vol_parts[0])
                vol_max = float(vol_parts[1]) if len(vol_parts) > 1 else vol_min * 2

            # Calculate volatility mismatch
            if vol_min <= current_volatility <= vol_max:
                volatility_risk = 0.2  # Low risk when in optimal range
            elif current_volatility < vol_min:
                volatility_risk = 0.4 + (vol_min - current_volatility) / vol_min * 0.4
            else:
                volatility_risk = 0.4 + (current_volatility - vol_max) / vol_max * 0.4

            return min(volatility_risk, 1.0)

        except Exception as e:
            print(f"Volatility risk assessment failed: {e}")
            return 0.5  # Neutral risk

    def _assess_liquidity_risk(self, current_price: float) -> float:
        """Assess liquidity risk based on order book depth"""
        try:
            order_book = self.fetch_order_book()

            # Calculate bid-ask spread
            best_bid = order_book['bids'][0][0] if order_book['bids'] else current_price
            best_ask = order_book['asks'][0][0] if order_book['asks'] else current_price
            spread = (best_ask - best_bid) / current_price

            # Calculate order book depth
            bid_depth = sum(bid[1] for bid in order_book['bids'][:10])
            ask_depth = sum(ask[1] for ask in order_book['asks'][:10])
            total_depth = bid_depth + ask_depth

            # Risk increases with wider spreads and lower depth
            spread_risk = min(spread * 1000, 1.0)  # Normalize spread
            depth_risk = max(0, 1.0 - total_depth / self.liquidity_threshold)

            return (spread_risk + depth_risk) / 2

        except Exception as e:
            print(f"Liquidity risk assessment failed: {e}")
            return 0.5

    def _assess_trend_risk(self, market_data: pd.DataFrame, strategy: Strategy) -> float:
        """Assess trend risk based on current trend vs strategy preference"""
        try:
            # Calculate trend strength using multiple indicators
            close_prices = market_data['close']

            # Moving average trend
            ma_short = close_prices.rolling(window=20).mean()
            ma_long = close_prices.rolling(window=50).mean()
            ma_trend = 1 if ma_short.iloc[-1] > ma_long.iloc[-1] else -1

            # Price momentum
            momentum = (close_prices.iloc[-1] - close_prices.iloc[-20]) / close_prices.iloc[-20]
            momentum_trend = 1 if momentum > 0 else -1

            # Determine current market trend
            if ma_trend == 1 and momentum_trend == 1:
                current_trend = "bullish"
            elif ma_trend == -1 and momentum_trend == -1:
                current_trend = "bearish"
            else:
                current_trend = "range"

            # Compare with strategy's optimal trend
            optimal_trend = strategy.optimal_trend.lower()

            if optimal_trend == current_trend:
                return 0.2  # Low risk when trends align
            elif optimal_trend == "range" or current_trend == "range":
                return 0.5  # Medium risk in ranging markets
            else:
                return 0.8  # High risk when trends oppose

        except Exception as e:
            print(f"Trend risk assessment failed: {e}")
            return 0.5

    def _assess_sentiment_risk(self) -> float:
        """Assess sentiment risk using external sentiment data"""
        try:
            # Get recent sentiment data
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            sentiment_data = san.get(
                "sentiment_balance_total",
                slug="bitcoin",
                from_date=start_date,
                to_date=end_date,
                interval="1d"
            )

            if sentiment_data.empty:
                return 0.5

            # Calculate sentiment risk (extreme sentiment = higher risk)
            latest_sentiment = sentiment_data['value'].iloc[-1]
            normalized_sentiment = (latest_sentiment + 1) / 2  # Normalize to 0-1

            # Risk is higher at extremes
            sentiment_risk = 2 * abs(normalized_sentiment - 0.5)
            return min(sentiment_risk, 1.0)

        except Exception as e:
            print(f"Sentiment risk assessment failed: {e}")
            return 0.5

    def _assess_on_chain_risk(self) -> float:
        """Assess on-chain risk using blockchain metrics"""
        try:
            # This would require specific on-chain metrics
            # For now, return neutral risk
            return 0.5

        except Exception as e:
            print(f"On-chain risk assessment failed: {e}")
            return 0.5

    def _assess_funding_risk(self) -> float:
        """Assess funding rate risk"""
        try:
            funding_rate = self.get_funding_rate()

            # Higher absolute funding rates indicate higher risk
            funding_risk = min(abs(funding_rate) / self.config.BASE_PARAMS.get('FUNDING_RATE_THRESHOLD', 0.01), 1.0)
            return funding_risk

        except Exception as e:
            print(f"Funding risk assessment failed: {e}")
            return 0.5

    def _assess_correlation_risk(self, market_data: pd.DataFrame) -> float:
        """Assess correlation risk with broader markets"""
        try:
            # Calculate autocorrelation (trend persistence)
            returns = market_data['close'].pct_change().dropna()
            autocorr = returns.autocorr(lag=1)

            # Higher autocorrelation can indicate trending markets (lower diversification)
            correlation_risk = abs(autocorr) if not np.isnan(autocorr) else 0.5
            return correlation_risk

        except Exception as e:
            print(f"Correlation risk assessment failed: {e}")
            return 0.5

    def _assess_drawdown_risk(self, portfolio_value: float) -> float:
        """Assess current drawdown risk"""
        try:
            # This would require portfolio history
            # For now, return low risk
            return 0.3

        except Exception as e:
            print(f"Drawdown risk assessment failed: {e}")
            return 0.5

    # === Additional Helper Methods ===

    def _classify_risk_level(self, risk_score: float) -> Tuple[RiskLevel, float]:
        """Classify risk level and return position multiplier"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW, 1.2
        elif risk_score < 0.4:
            return RiskLevel.LOW, 1.0
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM, 0.8
        elif risk_score < 0.8:
            return RiskLevel.HIGH, 0.5
        elif risk_score < 0.9:
            return RiskLevel.VERY_HIGH, 0.2
        else:
            return RiskLevel.EXTREME, 0.0

    def _get_strategy_risk_aversion(self, strategy_params: Dict) -> float:
        """Extract risk aversion from strategy parameters"""
        try:
            market_params = strategy_params.get('market_parameters', {})
            action_sensitivities = market_params.get('action_sensitivities', {})
            return action_sensitivities.get('risk_aversion', 0.5)
        except:
            return 0.5

    def _get_strategy_position_aggressiveness(self, strategy_params: Dict) -> float:
        """Extract position aggressiveness from strategy parameters"""
        try:
            market_params = strategy_params.get('market_parameters', {})
            action_sensitivities = market_params.get('action_sensitivities', {})
            return action_sensitivities.get('position_sizing_aggressiveness', 1.0)
        except:
            return 1.0

    def _calculate_dynamic_stop_loss(self, current_price: float, position_type: str,
                                   atr: float, risk_aversion: float) -> float:
        """Calculate dynamic stop loss based on ATR and risk aversion"""
        atr_multiplier = 1.5 + risk_aversion  # More risk averse = tighter stops

        if position_type == "long":
            return current_price - (atr * atr_multiplier)
        else:  # short
            return current_price + (atr * atr_multiplier)

    def _calculate_dynamic_take_profit(self, current_price: float, position_type: str,
                                     atr: float, aggressiveness: float) -> float:
        """Calculate dynamic take profit based on ATR and aggressiveness"""
        atr_multiplier = 2.0 * aggressiveness  # More aggressive = wider targets

        if position_type == "long":
            return current_price + (atr * atr_multiplier)
        else:  # short
            return current_price - (atr * atr_multiplier)

    def _estimate_strategy_win_rate(self, strategy: Strategy) -> float:
        """Estimate strategy win rate based on historical performance"""
        try:
            if "trend_following" in strategy.favored_patterns:
                return 0.45  # Trend following typically has lower win rate but higher avg win
            elif "mean_reversion" in strategy.favored_patterns:
                return 0.65  # Mean reversion typically has higher win rate
            elif "momentum" in strategy.favored_patterns:
                return 0.50  # Momentum strategies are balanced
            else:
                return 0.50  # Default
        except:
            return 0.50

    def _estimate_win_loss_ratio(self, current_price: float, stop_loss: float, take_profit: float) -> float:
        """Estimate average win/loss ratio"""
        try:
            potential_loss = abs(current_price - stop_loss)
            potential_gain = abs(take_profit - current_price)
            return potential_gain / potential_loss if potential_loss > 0 else 2.0
        except:
            return 2.0

    def _calculate_kelly_fraction(self, win_rate: float, win_loss_ratio: float) -> float:
        """Calculate Kelly Criterion fraction"""
        try:
            kelly = (win_rate * win_loss_ratio - (1 - win_rate)) / win_loss_ratio
            # Cap Kelly fraction to prevent over-leveraging
            return max(0, min(kelly, 0.25))
        except:
            return 0.1

    def _calculate_strategy_multiplier(self, strategy: Strategy, market_data: pd.DataFrame,
                                     current_price: float) -> float:
        """Calculate strategy-specific position multiplier"""
        try:
            # Base multiplier
            multiplier = 1.0

            # Adjust based on strategy parameters
            strategy_params = strategy.parameters
            market_params = strategy_params.get('market_parameters', {})

            # Signal strength adjustments
            signal_strengths = market_params.get('signal_strengths', {})
            avg_signal_strength = np.mean(list(signal_strengths.values())) if signal_strengths else 0.5
            multiplier *= (0.5 + avg_signal_strength)

            return multiplier
        except:
            return 1.0

    def _calculate_risk_reward_ratio(self, current_price: float, stop_loss: float,
                                   take_profit: float, position_type: str) -> float:
        """Calculate risk-reward ratio"""
        try:
            if position_type == "long":
                risk = current_price - stop_loss
                reward = take_profit - current_price
            else:  # short
                risk = stop_loss - current_price
                reward = current_price - take_profit

            return reward / risk if risk > 0 else 0
        except:
            return 0

    def _create_neutral_position(self) -> PositionSizing:
        """Create neutral position sizing for no-trade scenarios"""
        return PositionSizing(
            base_position_size=0.0,
            risk_adjusted_size=0.0,
            max_allowed_size=0.0,
            final_position_size=0.0,
            stop_loss_price=0.0,
            take_profit_price=0.0,
            risk_reward_ratio=0.0
        )

