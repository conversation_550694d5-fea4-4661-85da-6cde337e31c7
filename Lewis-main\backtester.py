import pandas as pd
import numpy as np
import multiprocessing
import threading
import queue as queue
from typing import Dict, Any, List, Tuple
from sklearn.model_selection import train_test_split
from deap import base, creator, tools, algorithms
import random
from scipy import stats
from strategy import Strategy
from risk_manager import RiskManager
import strategy
from strategy_generator import StrategyGenerator
from strategy_selector import StrategySelector
from config import Config
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import zipfile
import io
from io import StringIO
import traceback
from strategy_optimizer import StrategyOptimizer
from collections import deque
from event import EventType, MarketEvent, SignalEvent, OrderEvent, FillEvent
import time
from market_simulator import MarketSimulator
historical_data = pd.read_csv('historical_data.csv.zip')
from strategy import TimeFrame
from strategy import (TrendFollowingStrategy, MeanReversionStrategy, MomentumStrategy, 
                      VolatilityStrategy, StatisticalArbitrageStrategy, 
                      SentimentAnalysisStrategy, BreakoutStrategy)
from strategy_manager import StrategyManager
import tracemalloc
tracemalloc.start()
import asyncio
import re
from datetime import datetime

# Import signal generator components
try:
    from signal_generator import (
        train_all,
        run_training_loop,
        StrategyEvaluator,
        DataManager,
        ParameterCalculator,
        WINDOW_CONFIG
    )
    SIGNAL_GENERATOR_AVAILABLE = True
except ImportError:
    SIGNAL_GENERATOR_AVAILABLE = False

class Backtester(multiprocessing.Process):  # or threading.Thread
    def __init__(self, config, api_call_manager, historical_data: pd.DataFrame, result_queue, end_time: datetime | None = None):
        super().__init__()
        self.max_concurrent_backtesters = config.BASE_PARAMS.get('MAX_CONCURRENT_BACKTESTERS')
        self.max_concurrent_strategies = config.BASE_PARAMS.get('MAX_CONCURRENT_STRATEGIES')
        self.progress_update_interval = config.BASE_PARAMS.get('PROGRESS_UPDATE_INTERVAL')  
        self.end_time = end_time
        self.api_call_manager = api_call_manager
        self.results_queue = result_queue
        # Validate input data
        if historical_data.empty:
            raise ValueError("Historical data is empty")
            
        if not all(col in historical_data.columns for col in ['open', 'high', 'low', 'close', 'volume']):
            raise ValueError("Missing required columns in historical data")
            
        print(f"Initializing backtester with {len(historical_data)} data points")
        
        self.config = config
        print(f"Backtester config BASE_PARAMS: {hasattr(self.config, 'BASE_PARAMS')}")
        self.historical_data = historical_data        
        # Initialize strategies with proper strategy objects
        self.strategies = []
        for timeframe in TimeFrame:
            strategy = TrendFollowingStrategy(
                self.config,
                time.time(),
                timeframe,
                self.config.ADAPTIVE_PARAMS['TREND_FOLLOWING_PARAMS']
            )
            self.strategies.append(strategy)
        
        self.events = deque()
        self.current_position = 0
        self.cash = float(config.BASE_PARAMS['INITIAL_CAPITAL'])
        self.portfolio_value = self.cash
        self.strategies = {
            TimeFrame.SHORT_TERM: {},
            TimeFrame.MID_TERM: {},
            TimeFrame.LONG_TERM: {},
            TimeFrame.SEASONAL_TERM: {}
        }
        self.total_return = 0.0
        self.sharpe_ratio = 0.0
        self.max_drawdown = 0.0
        self.win_rate = 0.0
        self.profit_factor = 0.0
        self.impermanent_loss = 0.0
        self.strategy_types = [
            TrendFollowingStrategy,
            MeanReversionStrategy,
            MomentumStrategy,
            VolatilityStrategy,
            BreakoutStrategy,
            StatisticalArbitrageStrategy,
            SentimentAnalysisStrategy,
        ]
        for time_frame in TimeFrame:
            self.strategies[time_frame] = {}
        self.trades = []
        self.portfolio_values = []
        self.stop_event = multiprocessing.Event()
        self.strategy_generator = StrategyGenerator(config)
        self.market_simulator = MarketSimulator(config, strategy)
        self.risk_manager = RiskManager(config)
        self.strategy_optimizer = StrategyOptimizer(config, MarketSimulator, self.strategies)
        self.strategy_manager = StrategyManager(config, api_call_manager, use_ai_selection=False)
        self.open_positions: Dict[str, dict] = {}
        # key = strategy_name, value = {direction, entry_price, quantity, stop_loss, ...}
        self.cash_per_strategy: Dict[str, float] = {}

        self.asyncio = asyncio
        # Progress tracking
        self.total_events = len(historical_data)
        self.processed_events = 0

        # Signal generator components
        self.signal_generator_enabled = SIGNAL_GENERATOR_AVAILABLE and config.BASE_PARAMS.get('USE_SIGNAL_GENERATOR', True)
        self.strategy_evaluator = None
        self.data_manager = None
        self.parameter_calculator = None

        # Enhanced tracking
        self.strategy_signals = {}  # Track AI-generated signals
        self.signal_performance = {}  # Track signal performance
        self.learning_iterations = 0
        self.last_training_time = time.time()
        self.training_interval = config.BASE_PARAMS.get('SIGNAL_TRAINING_INTERVAL', 3600)  # 1 hour

        # Performance comparison
        self.original_signals = {}  # Original strategy signals
        self.enhanced_signals = {}  # AI-enhanced signals
        self.signal_improvements = {}  # Track improvements

    # This runs when you use .start()
    def run(self):
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(self._async_run())
            loop.close()
            return results
        except Exception as e:
            print(f"[ERROR] Backtester run failure: {e}")
            raise

    # This is where everything happens
    async def _async_run(self):
        """
        Run one full back-test cycle repeatedly until the TradingSystem’s
        runtime limit (self.end_time) is reached.
        """
        await self.initialize()

        while not self.stop_event.is_set() and \
              (self.end_time is None or datetime.now() < self.end_time):

            # ❷ MAIN back-test pass over historical data
            for ts, row in self.historical_data.iterrows():
                self.processed_events += 1
                if self.processed_events % self.progress_update_interval == 0:
                    pct = 100 * self.processed_events / self.total_events
                    print(f"Backtest progress: {pct:5.2f}%")

                market_event = self.create_market_event(ts, row)
                await self.process_event(market_event)

            # ❸ Push results from this pass
            results = await self.get_results()
            self.results_queue.put(results)

            # ❹ If time-limit not reached, reset state and do another pass
            if self.end_time is None or datetime.now() < self.end_time:
                self.reset()                # clear positions, trades, etc.
                await self.initialize()     # fresh strategies for next pass
                self.processed_events = 0
                continue
            else:
                break                       # runtime expired → exit loop

        # Return latest results for join()
        return results

    async def initialize(self):
        """Initialize backtesting environment"""
        self.portfolio_value = self.config.BASE_PARAMS["INITIAL_CAPITAL"]
        self.positions = {}
        self.trades = []
        await self.strategy_manager.initialize_strategies(
            self.strategy_generator,
            self.historical_data
        )

        # Initialize signal generator if enabled
        if self.signal_generator_enabled:
            await self._initialize_signal_generator()

    async def _initialize_signal_generator(self):
        """Initialize the AI signal generator components"""
        try:
            if not SIGNAL_GENERATOR_AVAILABLE:
                print("Signal generator not available - falling back to basic backtesting")
                self.signal_generator_enabled = False
                return

            print("Initializing AI signal generator")

            # Initialize data manager with current data
            self.data_manager = DataManager(
                hist_csv=None,  # We'll provide data directly
                strat_json='strategies.json',
                senti_csv=None  # Optional sentiment data
            )

            # Set up data manager with our historical data
            self.data_manager.timeframes = {
                'short_term': self.historical_data.resample('1H').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }),
                'mid_term': self.historical_data.resample('4H').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }),
                'long_term': self.historical_data.resample('1D').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }),
                'seasonal_term': self.historical_data.resample('1W').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                })
            }

            # Initialize parameter calculator
            self.parameter_calculator = ParameterCalculator()

            # Initialize strategy evaluator
            self.strategy_evaluator = StrategyEvaluator(
                market_feat_dim=50,  # Adjust based on features
                embed_dim=256
            )

            # Try to load pre-trained weights
            try:
                import torch
                self.strategy_evaluator.load_state_dict(torch.load('evaluator_weights.pth'))
                print("Loaded pre-trained signal generator weights")
            except:
                print("No pre-trained weights found - using fresh model")

        except Exception as e:
            print(f"Failed to initialize signal generator: {e}")
            self.signal_generator_enabled = False

    async def stop(self):
        self.stop_event.set()
        start_time = time.time()
        processed_rows = 0
    
        try:
            while not self.stop_event.is_set():
                for timestamp, row in self.historical_data.iterrows():
                    processed_rows += 1
                    if processed_rows % 100000 == 0:
                        print(f"Processed {processed_rows}/{len(self.historical_data)} rows")
            
                    if self.stop_event.is_set():
                        break
                
                market_event = MarketEvent(timestamp, row.to_dict())
                self.events.append(market_event)
                await self.process_events()  # Now this will work correctly
                self.update_portfolio_value(timestamp)
                self.portfolio_values.append((timestamp, self.portfolio_value))

        except Exception as e:
            print(f"Error during backtest: {e}")
            raise

        print(f"Backtest completed in {time.time() - start_time:.2f} seconds")
    
    def get_recent_data(self, timestamp_or_event, lookback_periods=100):
        """Get recent market data up to the specified timestamp"""
        # Handle both dictionary events and direct timestamps
        if isinstance(timestamp_or_event, dict):
            timestamp = timestamp_or_event['timestamp']
        else:
            timestamp = timestamp_or_event
        
        # Find the index of the current timestamp
        current_idx = self.historical_data.index.get_loc(timestamp)
    
        # Get data from lookback_periods ago up to current timestamp
        start_idx = max(0, current_idx - lookback_periods)
        recent_data = self.historical_data.iloc[start_idx:current_idx + 1]
    
        return recent_data
    
    def update_portfolio_value(self, timestamp):
        """Update the current portfolio value based on positions and current market price"""
        current_price = self.historical_data.loc[timestamp, 'close']
        self.portfolio_value = self.cash + (self.current_position * current_price)
        return self.portfolio_value

    async def execute_trade(self, signal, event):
        try:
            price = self.historical_data.loc[event.timestamp, 'close']
            portfolio_value = self.get_portfolio_value()
            recent_data = self.get_recent_data(event.timestamp)

            # Make sure current_strategy is set BEFORE risk management + entry check
            strategy = self.current_strategy
            if strategy is None:
                # If no strategy is set, we can’t decide—skip
                return None

            # 1) Risk‐managed position sizing
            position_size, stop_loss, take_profit = self.backtest_risk_manager.apply_backtest_risk_management(
                signal,
                portfolio_value,
                price,
                recent_data,
                strategy
            )

            # 2) Skip if no valid position size
            if position_size <= 0:
                self.logger.info("Position size is zero — skipping trade.")
                return None

            # 3) New: Check strategy conditions here
            if not self.should_execute_trade(signal, recent_data, strategy):
                self.logger.info("Strategy conditions not satisfied for trade execution.")
                return None

            # 4) Trailing stop logic (unchanged)
            trailing_stop_pct = strategy.parameters.get("TRAILING_STOP")
            trailing_stop_price = None
            if trailing_stop_pct:
                if signal > 0:
                    trailing_stop_price = price * (1 - trailing_stop_pct)
                elif signal < 0:
                    trailing_stop_price = price * (1 + trailing_stop_pct)

            # 5) Calculate quantity & commission
            quantity = position_size / price if price > 0 else 0
            commission = await self.calculate_commission(quantity, price)

            # 6) Execute BUY or SELL
            if signal > 0:
                self.cash -= (price * quantity + commission)
                self.current_position += quantity
                direction = "BUY"
            elif signal < 0:
                self.cash += (price * quantity - commission)
                self.current_position -= quantity
                direction = "SELL"
            else:
                return None

            trade = {
                'timestamp': event.timestamp,
                'direction': direction,
                'quantity': quantity,
                'price': price,
                'commission': commission,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'portfolio_value': self.cash + self.current_position * price,
                'trailing_stop': trailing_stop_price
            }

            self.trades.append(trade)
            self.portfolio_values.append((event.timestamp, trade['portfolio_value']))

            self.logger.info(f"Trade executed: {direction} {quantity:.4f} @ {price:.2f}")
            return trade

        except Exception as e:
            self.logger.error(f"Error executing trade: {str(e)}")
            self.logger.error(traceback.format_exc())
            return None

    async def process_events(self):
        events_processed = 0
        while self.events:
            event = self.events.popleft()
            events_processed += 1
            
            if events_processed % 100 == 0:
                print(f"Processing event batch {events_processed}")
                
            if event.type == EventType.MARKET:
                await self.handle_market_event(event)
            elif event.type == EventType.SIGNAL:
                await self.handle_signal_event(event)
            elif event.type == EventType.ORDER:
                await self.handle_order_event(event)
            elif event.type == EventType.FILL:
                await self.handle_fill_event(event)

    async def handle_market_event(self, event):
        """Handle market update. Each strategy can open at most one position at a time.
        Each new trade uses exactly 25% of total capital; up to 4 strategies may be open."""
        timestamp = event['timestamp']
        recent_data = self.get_recent_data(timestamp)
        current_price = recent_data['close'].iloc[-1]

        # 1) Close any position that hit its stop-loss or take-profit
        await self._check_and_close_positions(timestamp, current_price)

        # 2) loop through every timeframe — update + detect removals
        removed: set[str] = set()                 # names of strategies that vanished
        for tf in TimeFrame:
            before = set(self.strategy_manager.strategies.get(tf, {}).keys())

            # update_strategies now receives the tf argument
            await self.strategy_manager.update_strategies(recent_data, use_ai_selection=False, time_frame=tf)

        # 3) force-close any removed strategies that still have open positions
        for name in removed:
            if name in self.open_positions:
                self.force_close_strategy(name, timestamp, current_price)

        # 4) recompute total capital after closures
        total_capital = self._get_total_capital(current_price)

        # 5) if already four open, we’re done for this tick
        if len(self.open_positions) >= 4:
            return

        # 6) Recompute total capital after any closures
        total_capital = self._get_total_capital(current_price)

        # 7) If 4 strategies are already open, no new trades can open this tick
        if len(self.open_positions) >= 4:
            return

        # 8) For each timeframe, retrieve strategies that can trade now
        for timeframe in TimeFrame:
            selected = self.strategy_manager.get_selected_strategies(timeframe)

            for name, strategy in selected.items():
                # If this strategy already has an open position, skip
                if name in self.open_positions:
                    continue

                # Recompute total_capital in case anything changed
                total_capital = self._get_total_capital(current_price)

                # If 4 positions are open after closures, exit
                if len(self.open_positions) >= 4:
                    return

                # 9) Generate a signal
                signal = strategy.generate_signal(recent_data)
                if signal == 0:
                    continue

                # 10) Allocate 25% of total_capital to this new trade
                allocation = 0.25 * total_capital
                if allocation <= 0:
                    continue

                # 11) Compute quantity = allocation / price
                quantity = allocation / current_price

                # 12) Compute commission (optional) and stop/TP from risk manager
                commission = await self.calculate_commission(quantity, current_price)
                _, stop_loss, take_profit = self.backtest_risk_manager.apply_backtest_risk_management(
                    signal, total_capital, current_price, recent_data, strategy
                )
                # We override risk-manager’s position size to match our 25% allocation

                # 13) Log
                self.logger.info(
                    f"[{timestamp}] Strategy \"{name}\" OPEN signal {signal:+.0f}; "
                    f"allocating ${allocation:,.2f} ({quantity:.4f} units)"
                )

                # 14) Adjust cash: lock 'allocation' + commission
                if signal > 0:
                    self.cash -= (allocation + commission)
                else:
                    # For SELL (short), lock commission (and margin if you want)
                    self.cash -= commission

                # 15) Store the open position under this strategy
                trailing_stop_price = None
                trail_pct = strategy.parameters.get("TRAILING_STOP", None)
                if trail_pct:
                    if signal > 0:
                        trailing_stop_price = current_price * (1 - trail_pct)
                    else:
                        trailing_stop_price = current_price * (1 + trail_pct)

                self.open_positions[name] = {
                    'strategy': strategy,
                    'direction': "BUY" if signal > 0 else "SELL",
                    'entry_price': current_price,
                    'quantity': quantity,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'trailing_stop': trailing_stop_price,
                    'time_frame': timeframe
                }

                # 16) Record trade (entry) in self.trades
                self.trades.append({
                    'timestamp': timestamp,
                    'strategy_name': name,
                    'direction': "BUY" if signal > 0 else "SELL",
                    'quantity': quantity,
                    'price': current_price,
                    'commission': commission
                })

                # 17) Recompute total_capital and possibly exit if 4 open
                total_capital = self._get_total_capital(current_price)
                if len(self.open_positions) >= 4:
                    return

        # 18) Finally, update portfolio value snapshot
        self.update_portfolio_value(timestamp)
        self.portfolio_values.append((
            timestamp,
            self.cash + sum(
                (pos['quantity'] * current_price
                 if pos['direction'] == "BUY"
                 else pos['quantity'] * (2 * pos['entry_price'] - current_price))
                for pos in self.open_positions.values()
            )
        ))

    async def handle_signal_event(self, event: SignalEvent):
        order_type = 'MARKET'
        quantity, stop_loss, take_profit = await self.risk_manager.apply_risk_management(
            event.signal,
            self.portfolio_value,
            self.historical_data.loc[event.timestamp, 'close'],
            self.get_recent_data(event.timestamp)
        )
        if quantity != 0:
            self.events.append(OrderEvent(event.timestamp, event.symbol, order_type, quantity, 'BUY' if event.signal > 0 else 'SELL', stop_loss, take_profit))

    async def handle_order_event(self, event: OrderEvent):
        fill_cost = self.historical_data.loc[event.timestamp, 'close']
        commission = await self.calculate_commission(event.quantity, fill_cost)
        market_impact = await self.risk_manager.calculate_market_impact(event.quantity, fill_cost)
        total_cost = fill_cost + commission + market_impact
        self.events.append(FillEvent(event.timestamp, event.symbol, 'BACKTEST', event.quantity, event.direction, total_cost, commission))

    async def handle_fill_event(self, event: FillEvent):
        if event.direction == 'BUY':
            self.cash -= (event.fill_cost * event.quantity + event.commission)
            self.current_position += event.quantity
        else:  # SELL
            self.cash += (event.fill_cost * event.quantity - event.commission)
            self.current_position -= event.quantity

        await self.record_trade({
            'timestamp': event.timestamp,
            'direction': event.direction,
            'quantity': event.quantity,
            'price': event.fill_cost,
            'commission': event.commission
        })
    
    def create_market_event(self, timestamp, data):
        return {
            'timestamp': timestamp,
            'price': data['close'],
            'volume': data['volume'],
            'type': 'market_update'
        }
    async def process_event(self, event):
        """Process individual market events and generate signals"""
        if event['type'] == 'market_update':
            # Access dictionary using square bracket notation
            recent_data = self.get_recent_data(event['timestamp'])
            await self.handle_market_event(event)

            # Add AI signal enhancement if enabled
            if self.signal_generator_enabled:
                await self._process_ai_signals(event)

            # Update portfolio using same access method
            self.update_portfolio_value(event['timestamp'])
            self.portfolio_values.append((event['timestamp'], self.portfolio_value))
        elif event.type == EventType.SIGNAL:
            await self.handle_signal_event(event)
        elif event.type == EventType.ORDER:
            await self.handle_order_event(event)
        elif event.type == EventType.FILL:
            await self.handle_fill_event(event)

    async def _process_ai_signals(self, event):
        """Process AI-enhanced signals for strategies"""
        try:
            timestamp = event['timestamp']
            recent_data = self.get_recent_data(timestamp)

            if recent_data is None or len(recent_data) < 50:
                return

            # Get active strategies for each timeframe
            for timeframe in TimeFrame:
                strategies = self.strategy_manager.strategies.get(timeframe, {})

                for strategy_name, strategy in strategies.items():
                    # Generate original signal
                    original_signal = strategy.generate_signal(recent_data)

                    # Generate AI-enhanced signal
                    enhanced_signal = await self._generate_enhanced_signal(
                        strategy, recent_data, timeframe
                    )

                    # Store signals for comparison
                    strategy_key = f"{strategy_name}_{timeframe.value}"
                    self.original_signals[strategy_key] = original_signal
                    self.enhanced_signals[strategy_key] = enhanced_signal

                    # Use enhanced signal if available and different
                    if enhanced_signal is not None and abs(enhanced_signal - original_signal) > 0.1:
                        # Override strategy signal with enhanced version
                        strategy._last_signal = enhanced_signal

        except Exception as e:
            print(f"Error processing AI signals: {e}")

    async def _generate_enhanced_signal(self, strategy, market_data: pd.DataFrame,
                                      timeframe) -> float:
        """Generate AI-enhanced signal for a strategy"""
        try:
            if not self.strategy_evaluator:
                return None

            # Prepare market features
            market_features = self._extract_market_features(market_data, timeframe)
            if market_features is None:
                return None

            # Prepare strategy parameters
            strategy_params = self._extract_strategy_parameters(strategy)

            # Generate enhanced signal using the AI model
            import torch

            with torch.no_grad():
                market_tensor = torch.FloatTensor(market_features).unsqueeze(0)
                param_tensor = torch.FloatTensor(strategy_params).unsqueeze(0)

                # Get AI evaluation
                evaluation = self.strategy_evaluator(market_tensor, param_tensor)
                enhanced_signal = evaluation.item()

                # Normalize to [-1, 1] range
                enhanced_signal = np.tanh(enhanced_signal)

                return enhanced_signal

        except Exception as e:
            print(f"Failed to generate enhanced signal: {e}")
            return None

    def _extract_market_features(self, market_data: pd.DataFrame, timeframe) -> List[float]:
        """Extract market features for AI model"""
        try:
            if len(market_data) < 20:
                return None

            # Use parameter calculator to extract features
            data_dict = {
                'close': market_data['close'],
                'high': market_data['high'],
                'low': market_data['low'],
                'volume': market_data['volume'],
                'sentiment': pd.Series([0.5] * len(market_data))  # Neutral sentiment fallback
            }

            # Calculate basic parameters
            calc_params = {
                'calculation_parameters': {
                    'MOVING_AVERAGE_SHORT': 10,
                    'MOVING_AVERAGE_LONG': 20,
                    'RSI_PERIOD': 14,
                    'MACD_FAST': 12,
                    'MACD_SLOW': 26
                }
            }

            features = self.parameter_calculator.calculate_all(data_dict, calc_params)

            # Convert to list and pad/truncate to fixed size
            feature_list = list(features.values())
            target_size = 50

            if len(feature_list) < target_size:
                feature_list.extend([0.0] * (target_size - len(feature_list)))
            else:
                feature_list = feature_list[:target_size]

            return feature_list

        except Exception as e:
            print(f"Failed to extract market features: {e}")
            return None

    def _extract_strategy_parameters(self, strategy) -> List[float]:
        """Extract strategy parameters for AI model"""
        try:
            params = strategy.parameters

            # Extract key parameters and normalize
            param_values = []

            # Market parameters
            market_params = params.get('market_parameters', {})
            for group in ['signal_strengths', 'directional_biases', 'action_sensitivities']:
                group_params = market_params.get(group, {})
                param_values.extend(list(group_params.values()))

            # Calculation parameters
            calc_params = params.get('calculation_parameters', {})
            param_values.extend(list(calc_params.values()))

            # Normalize and pad to fixed size
            target_size = 30
            if len(param_values) < target_size:
                param_values.extend([0.0] * (target_size - len(param_values)))
            else:
                param_values = param_values[:target_size]

            # Normalize values to reasonable range
            normalized_params = []
            for val in param_values:
                if isinstance(val, (int, float)):
                    # Simple normalization
                    normalized_val = np.tanh(val / 100.0)  # Adjust scaling as needed
                    normalized_params.append(normalized_val)
                else:
                    normalized_params.append(0.0)

            return normalized_params

        except Exception as e:
            print(f"Failed to extract strategy parameters: {e}")
            return [0.0] * 30  # Return zeros as fallback

    async def _periodic_training(self):
        """Perform periodic training of the signal generator"""
        try:
            current_time = time.time()

            if current_time - self.last_training_time < self.training_interval:
                return

            if not self.signal_generator_enabled:
                return

            print("Starting periodic signal generator training")

            # Run training with recent performance data
            training_iterations = 10  # Quick training session
            training_results = run_training_loop(training_iterations)

            self.learning_iterations += training_iterations
            self.last_training_time = current_time

            print(f"Completed training - Total iterations: {self.learning_iterations}")

            # Save updated weights
            try:
                import torch
                torch.save(self.strategy_evaluator.state_dict(), 'evaluator_weights.pth')
                print("Saved updated signal generator weights")
            except Exception as e:
                print(f"Failed to save weights: {e}")

        except Exception as e:
            print(f"Periodic training failed: {e}")

    async def record_trade(self, trade_data):
        self.trades.append(trade_data)
        await self.update_strategy(trade_data['timestamp'], use_ai_selection = False)

    async def calculate_commission(self, quantity: int, price: float) -> float:
        return self.config.COMMISSION_RATE * quantity * price

    async def update_strategy(self, timestamp, use_ai_selection):
        try:
            self.use_ai_selection = use_ai_selection
            for time_frame in TimeFrame:
                await self.strategy_manager.update_strategies(
                    self.get_recent_data(timestamp),
                    time_frame,
                    self.use_ai_selection
                    )

        except Exception as e:
            self.logger.error(f"Error during strategy update: {str(e)}")
            raise

    def calculate_strategy_performance(self, time_frame: TimeFrame):
        return {name: strategy.calculate_performance(self.trades) for name, strategy in self.strategies[time_frame].items()}

    def calculate_performance_metrics(self):
        returns = pd.Series([pv for _, pv in self.portfolio_values]).pct_change()
        self.total_return = (self.portfolio_value - self.config.BASE_PARAMS['INITIAL_CAPITAL']) / self.config.BASE_PARAMS['INITIAL_CAPITAL']
        self.sharpe_ratio = np.sqrt(252) * returns.mean() / returns.std()
        self.max_drawdown = (returns.cumsum() - returns.cumsum().cummax()).min()
        
        # Add check for empty trades list
        if len(self.trades) > 0:
            self.win_rate = sum(1 for trade in self.trades if trade['direction'] == 'SELL' and trade['price'] > trade['price']) / len(self.trades)
            self.profit_factor = sum(trade['price'] - trade['price'] for trade in self.trades if trade['direction'] == 'SELL' and trade['price'] > trade['price']) / abs(sum(trade['price'] - trade['price'] for trade in self.trades if trade['direction'] == 'SELL' and trade['price'] < trade['price']))
        else:
            self.win_rate = 0
            self.profit_factor = 0
        

        self.impermanent_loss = self.calculate_impermanent_loss()

    def calculate_impermanent_loss(self):
        # Simplified impermanent loss calculation
        if len(self.trades) < 2:
            return 0

        initial_price = self.trades[0]['price']
        final_price = self.trades[-1]['price']
        price_ratio = final_price / initial_price

        impermanent_loss = 2 * np.sqrt(price_ratio) / (1 + price_ratio) - 1
        return impermanent_loss

    async def monte_carlo_simulation(self, num_simulations: int = 1000) -> Dict[str, List[float]]:
        returns = pd.Series([pv for _, pv in self.portfolio_values]).pct_change().dropna()
    
        # Check if we have enough data points
        if len(returns) == 0:
            return {
                'final_portfolio_values': [self.config.BASE_PARAMS['INITIAL_CAPITAL']],
                'max_drawdowns': [0.0],
                'sharpe_ratios': [0.0]
            }

        simulation_results = {
            'final_portfolio_values': [],
            'max_drawdowns': [],
            'sharpe_ratios': []
        }

        for _ in range(num_simulations):
            simulated_returns = np.random.choice(returns, size=len(returns), replace=True)
            cumulative_returns = (1 + simulated_returns).cumprod()
            portfolio_values = pd.Series(self.config.BASE_PARAMS['INITIAL_CAPITAL'] * cumulative_returns)

            if len(portfolio_values) > 0:
                final_value = portfolio_values.iloc[-1]
                max_drawdown = (portfolio_values / portfolio_values.cummax() - 1).min()
                sharpe_ratio = np.sqrt(252) * simulated_returns.mean() / simulated_returns.std()

                simulation_results['final_portfolio_values'].append(final_value)
                simulation_results['max_drawdowns'].append(max_drawdown)
                simulation_results['sharpe_ratios'].append(sharpe_ratio)

            await asyncio.sleep(0)  # Allow other coroutines to run

        return simulation_results    
        
    def run_mini_backtest(self, strategy):
        mini_trades = []
        for i in range(len(self.historical_data) - 1):
            signal = strategy.generate_signal(self.historical_data.iloc[i:i+2])
            if signal != 0:
                trade = {
                    'timestamp': self.historical_data.index[i+1],
                    'direction': 'BUY' if signal > 0 else 'SELL',
                    'quantity': 1,  # Simplified quantity
                    'price': self.historical_data.iloc[i+1]['close'],
                    'commission': self.calculate_commission(1, self.historical_data.iloc[i+1]['close'])
                }
                mini_trades.append(trade)
        return mini_trades

    def reset(self):
        self.current_position = 0
        self.cash = self.config.BASE_PARAMS['INITIAL_CAPITAL']
        self.portfolio_value = self.cash
        self.trades = []
        self.portfolio_values = []
        self.events.clear()

    def calculate_var(self, confidence_level=0.95):
        returns = pd.Series([pv for _, pv in self.portfolio_values]).pct_change().dropna()
        if len(returns) == 0:
            return 0.0
        var = np.percentile(returns, 100 * (1 - confidence_level))
        return self.portfolio_value * var

    def calculate_cvar(self, confidence_level=0.95):
        returns = pd.Series([pv for _, pv in self.portfolio_values]).pct_change().dropna()
        if len(returns) == 0:
            return 0.0
        var = np.percentile(returns, 100 * (1 - confidence_level))
        cvar = returns[returns <= var].mean()
        return self.portfolio_value * cvar

    async def get_results(self) -> Dict[str, Any]:
        mc_results = await self.monte_carlo_simulation()
        return {
            'total_return': self.total_return,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'final_portfolio_value': self.portfolio_value,
            'trades': self.trades,
            'var': self.calculate_var(),
            'cvar': self.calculate_cvar(),
            'impermanent_loss': self.impermanent_loss,
            'monte_carlo': {
                'mean_final_value': np.mean(mc_results['final_portfolio_values']),
                'median_final_value': np.median(mc_results['final_portfolio_values']),
                'mean_max_drawdown': np.mean(mc_results['max_drawdowns']),
                'mean_sharpe_ratio': np.mean(mc_results['sharpe_ratios'])
            }
        }
    def check_open_position(self, timestamp):
        if not self.open_position:
            return

        current_price = self.historical_data.loc[timestamp, 'close']
        pos = self.open_position
        strategy = pos['strategy']
        params = strategy.parameters

        # ✅ Only use trailing stop if the strategy explicitly has the param
        if 'TRAILING_STOP' in params and params['TRAILING_STOP'] > 0:
            trailing_stop_pct = params['TRAILING_STOP']
            if pos['direction'] == 'BUY':
                new_trailing = current_price - current_price * trailing_stop_pct
                pos['stop_loss'] = max(pos['stop_loss'], new_trailing)
            elif pos['direction'] == 'SELL':
                new_trailing = current_price + current_price * trailing_stop_pct
                pos['stop_loss'] = min(pos['stop_loss'], new_trailing)

        # Standard SL/TP logic
        if pos['direction'] == 'BUY':
            if current_price <= pos['stop_loss']:
                self.close_position("STOP_LOSS", timestamp, current_price)
            elif current_price >= pos['take_profit']:
                self.close_position("TAKE_PROFIT", timestamp, current_price)
        elif pos['direction'] == 'SELL':
            if current_price >= pos['stop_loss']:
                self.close_position("STOP_LOSS", timestamp, current_price)
            elif current_price <= pos['take_profit']:
                self.close_position("TAKE_PROFIT", timestamp, current_price)

    def _close_position(self, strategy_name, timestamp, price, reason):
        pos = self.open_positions.pop(strategy_name)
        quantity = pos['quantity']
        direction = pos['direction']
        strat = next(s for s in self.strategy_manager.strategies[pos['strategy_timeframe']].values()
                     if s.name == strategy_name)

        # Adjust cash/position
        if direction == 'BUY':
            self.cash += quantity * price
            self.current_position -= quantity
            close_dir = f"CLOSE_BUY"
        else:
            self.cash -= quantity * price
            self.current_position += quantity
            close_dir = f"CLOSE_SELL"

        trade = {
            'timestamp': timestamp,
            'strategy_name': strategy_name,
            'direction': close_dir,
            'quantity': quantity,
            'price': price,
            'reason': reason,
            'portfolio_value': self.cash + self.current_position * price
        }
        self.trades.append(trade)
        self.logger.info(f"[{timestamp}] {strategy_name} {close_dir} @ {price:.2f} ({reason})")

    def should_execute_trade(self, signal: float, recent_data: pd.DataFrame, strategy) -> bool:
        """
        Decide whether to execute a trade based on the strategy’s favored patterns and parameters.
        Returns True if any of the entry conditions from favored_patterns are met.
        """
        params = strategy.parameters
        patterns = strategy.favored_patterns
        entry_flags = []

        #### 1) MEAN REVERSION ####
        if "mean_reversion" in patterns:
            window = params.get('MEAN_WINDOW', params.get('BOLLINGER_PERIOD', 20))
            std_mult = params.get('STD_MULTIPLIER', params.get('BOLLINGER_STD', 2.0))
            rolling_close = recent_data['close'].rolling(window)
            mean = rolling_close.mean().iloc[-1]
            std = rolling_close.std().iloc[-1]
            current_price = recent_data['close'].iloc[-1]
            if mean is None or std is None or std == 0:
                entry_flags.append(False)
            else:
                z_score = (current_price - mean) / std
                threshold_z = params.get('MEAN_REVERSION_THRESHOLD', params.get('ENTRY_DEVIATION', 1.0))
                entry_flags.append(abs(z_score) >= threshold_z)

        #### 2) BREAKOUT ####
        if "breakout" in patterns:
            period = params.get('BREAKOUT_PERIOD', 20)
            threshold = params.get('BREAKOUT_THRESHOLD', 0.01)
            confirm_candles = params.get('BREAKOUT_CONFIRMATION_CANDLES', 1)
            vol_mult = params.get('VOLUME_CONFIRMATION_MULT', 1.0)
            atr_period = params.get('ATR_PERIOD', None)

            if len(recent_data) < period + confirm_candles:
                entry_flags.append(False)
            else:
                high_past = recent_data['high'].iloc[-(period+1):-1].max()
                low_past = recent_data['low'].iloc[-(period+1):-1].min()
                current_close = recent_data['close'].iloc[-1]
                is_breakout_up = current_close > (high_past * (1 + threshold))
                is_breakout_down = current_close < (low_past * (1 - threshold))

                last_closes = recent_data['close'].tail(confirm_candles)
                if is_breakout_up:
                    confirm = all(last_closes > high_past * (1 + threshold))
                elif is_breakout_down:
                    confirm = all(last_closes < low_past * (1 - threshold))
                else:
                    confirm = False

                avg_vol = recent_data['volume'].rolling(period).mean().iloc[-2]  # exclude current bar
                current_vol = recent_data['volume'].iloc[-1]
                vol_ok = (current_vol >= vol_mult * avg_vol) if avg_vol is not None else True

                if atr_period:
                    tr1 = recent_data['high'] - recent_data['low']
                    tr2 = (recent_data['high'] - recent_data['close'].shift(1)).abs()
                    tr3 = (recent_data['low'] - recent_data['close'].shift(1)).abs()
                    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                    atr = tr.rolling(atr_period).mean().iloc[-1]
                    atr_ok = True  # or: atr >= params.get('VOLATILITY_BREAKOUT_THRESHOLD', 0)
                else:
                    atr_ok = True

                entry_flags.append(confirm and vol_ok and atr_ok)

        #### 3) MOMENTUM ####
        if "momentum" in patterns:
            mp = params.get('MOMENTUM_PERIOD', 5)
            thr = params.get('MOMENTUM_THRESHOLD', 0.02)
            if len(recent_data) <= mp:
                entry_flags.append(False)
            else:
                momentum = recent_data['close'].pct_change(mp).iloc[-1]
                base_cond = abs(momentum) >= thr

                rsi_ok = True
                rsi_period = params.get('RSI_PERIOD', None)
                if rsi_period:
                    delta = recent_data['close'].diff()
                    up = delta.clip(lower=0).rolling(rsi_period).mean()
                    down = -delta.clip(upper=0).rolling(rsi_period).mean()
                    rs = up / down
                    rsi = (100 - (100 / (1 + rs))).iloc[-1]
                    if rsi is None:
                        rsi_ok = False
                    else:
                        overbought = params.get('RSI_OVERBOUGHT', 70)
                        oversold = params.get('RSI_OVERSOLD', 30)
                        if signal > 0 and rsi >= overbought:
                            rsi_ok = False
                        if signal < 0 and rsi <= oversold:
                            rsi_ok = False

                entry_flags.append(base_cond and rsi_ok)

        #### 4) TREND FOLLOWING ####
        if "trend_following" in patterns:
            short_w = params.get('MOVING_AVERAGE_SHORT', 20)
            long_w = params.get('MOVING_AVERAGE_LONG', 50)
            strength_thr = params.get('TREND_STRENGTH_THRESHOLD', 0.0)
            confirm_period = params.get('TREND_CONFIRMATION_PERIOD', 1)

            if len(recent_data) < long_w + confirm_period:
                entry_flags.append(False)
            else:
                ma_short = recent_data['close'].rolling(short_w).mean()
                ma_long = recent_data['close'].rolling(long_w).mean()
                curr_short = ma_short.iloc[-1]
                curr_long = ma_long.iloc[-1]
                if curr_long == 0:
                    entry_flags.append(False)
                else:
                    sep = abs(curr_short - curr_long) / curr_long
                    strength_ok = sep >= strength_thr

                    last_shorts = ma_short.tail(confirm_period)
                    last_longs = ma_long.tail(confirm_period)
                    if signal > 0:
                        confirm = all(last_shorts > last_longs)
                    elif signal < 0:
                        confirm = all(last_shorts < last_longs)
                    else:
                        confirm = False

                    entry_flags.append(strength_ok and confirm)

        #### 5) VOLATILITY CLUSTERING ####
        if "volatility_clustering" in patterns:
            vw = params.get('VOLATILITY_WINDOW', 20)
            high_v_thr = params.get('HIGH_VOLATILITY_THRESHOLD', 0.02)
            low_v_thr = params.get('LOW_VOLATILITY_THRESHOLD', None)

            if len(recent_data) < vw + 1:
                entry_flags.append(False)
            else:
                returns = recent_data['close'].pct_change().dropna()
                vol = returns.rolling(vw).std().iloc[-1]
                if low_v_thr is not None:
                    cond_high = vol >= high_v_thr
                    cond_low = vol <= low_v_thr
                    entry_flags.append(cond_high or cond_low)
                else:
                    entry_flags.append(vol >= high_v_thr)

        #### 6) STATISTICAL ARBITRAGE ####
        if "statistical_arbitrage" in patterns:
            if 'close_A' in recent_data.columns and 'close_B' in recent_data.columns:
                lb = params.get('LOOKBACK_PERIOD', 60)
                if len(recent_data) < lb:
                    entry_flags.append(False)
                else:
                    sub = recent_data[['close_A','close_B']].iloc[-lb:]
                    corr = sub['close_A'].corr(sub['close_B'])
                    corr_ok = abs(corr) >= params.get('CORRELATION_THRESHOLD', 0.8)

                    if params.get('HEDGE_RATIO', None) is not None:
                        beta = params['HEDGE_RATIO']
                    else:
                        slope, intercept, r_val, p_val, std_err = stats.linregress(sub['close_B'], sub['close_A'])
                        beta = slope

                    spread = sub['close_A'] - beta * sub['close_B']
                    spread_mean = spread.mean()
                    spread_std = spread.std()
                    if spread_std == 0:
                        entry_flags.append(False)
                    else:
                        z_score = (spread.iloc[-1] - spread_mean) / spread_std
                        entry_thr = params.get('ENTRY_THRESHOLD', params.get('Z_SCORE_THRESHOLD', 2.0))
                        entry_flags.append(abs(z_score) >= entry_thr and corr_ok)
            else:
                entry_flags.append(False)

        #### 7) SENTIMENT ANALYSIS ####
        if "sentiment_analysis" in patterns:
            if 'sentiment_score' in recent_data.columns and 'sentiment_volume' in recent_data.columns:
                sw = params.get('SENTIMENT_WINDOW', 10)
                if len(recent_data) < sw:
                    entry_flags.append(False)
                else:
                    sent = recent_data['sentiment_score'].rolling(sw).mean().iloc[-1]
                    vol = recent_data['sentiment_volume'].rolling(sw).sum().iloc[-1]
                    pos_thr = params.get('POSITIVE_SENTIMENT_THRESHOLD', 0.5)
                    neg_thr = params.get('NEGATIVE_SENTIMENT_THRESHOLD', -0.5)
                    vol_thr = params.get('SENTIMENT_VOLUME_THRESHOLD', 1)

                    sent_mom = True
                    smp = params.get('SENTIMENT_MOMENTUM_PERIOD', None)
                    if smp and len(recent_data) >= sw + smp:
                        prev_sent = recent_data['sentiment_score'].rolling(sw).mean().shift(smp).iloc[-1]
                        sent_mom = (sent - prev_sent) > 0 if signal > 0 else (sent - prev_sent) < 0

                    if vol >= vol_thr:
                        if signal > 0 and sent >= pos_thr and sent_mom:
                            entry_flags.append(True)
                        elif signal < 0 and sent <= neg_thr and sent_mom:
                            entry_flags.append(True)
                        else:
                            entry_flags.append(False)
                    else:
                        entry_flags.append(False)
            else:
                entry_flags.append(False)

        if not patterns:
            return False

        return any(entry_flags)
    
    def _get_total_capital(self, current_price: float) -> float:
        """
        Compute total capital = cash + mark-to-market value of all open positions.
        For a BUY, value = quantity * current_price.
        For a SELL (short), value = quantity * (2*entry_price - current_price), but for now assume long-only.
        """
        positions_value = 0.0
        for pos in self.open_positions.values():
            qty = pos['quantity']
            direction = pos['direction']
            if direction == 'BUY':
                positions_value += qty * current_price
            else:  # SELL: assume short profit/loss: entry - current price
                entry = pos['entry_price']
                # short P&L: (entry_price - current_price) * quantity
                positions_value += qty * (2 * entry - current_price)
        return self.cash + positions_value
    
    def force_close_strategy(self, strategy_name: str, timestamp: datetime, current_price: float):
        """
        Force‐liquidate an open position for strategy_name without recording it
        as a normal trade (to avoid counting it as win/loss). Simply unlock capital.
        """
        if strategy_name not in self.open_positions:
            return

        pos = self.open_positions.pop(strategy_name)
        qty = pos['quantity']
        direction = pos['direction']

        # Re‐credit cash: use entry-based or mark‐to‐market value
        if direction == "BUY":
            # Return locked capital (entry * qty) back to cash
            entry_alloc = pos['entry_price'] * qty
            self.cash += entry_alloc
        else:
            # Short positions: unlock margin; assume same as entry
            entry_alloc = pos['entry_price'] * qty
            self.cash += entry_alloc

        # No trade record appended!
        self.logger.info(f"[{timestamp}] Force‐closed {strategy_name} at {current_price:.2f} (no P/L recorded)")

    async def _check_and_close_positions(self, timestamp: datetime, current_price: float):
        """
        For each open position, check if stop-loss or take-profit is hit. If yes,
        close it, credit cash + P/L, and append a trade record.
        """
        to_close = []
        for name, pos in self.open_positions.items():
            direction = pos['direction']
            stop = pos['stop_loss']
            tp   = pos['take_profit']

            if direction == 'BUY':
                if current_price <= stop or current_price >= tp:
                    to_close.append(name)
            else:  # SELL (short)
                # For simplicity, assume stop-loss above entry, take-profit below
                if current_price >= stop or current_price <= tp:
                    to_close.append(name)

        for name in to_close:
            pos = self.open_positions.pop(name)
            qty = pos['quantity']
            entry = pos['entry_price']
            direction = pos['direction']

            # Compute P/L
            if direction == 'BUY':
                pnl = (current_price - entry) * qty
                self.cash += (entry * qty) + pnl  # return entry + profit
            else:
                pnl = (entry - current_price) * qty
                self.cash += (entry * qty) + pnl  # return margin + profit

            # Record closing trade
            self.trades.append({
                'timestamp': timestamp,
                'strategy_name': name,
                'direction': f"CLOSE_{direction}",
                'quantity': qty,
                'price': current_price,
                'pnl': pnl,
                'portfolio_value': self._get_total_capital(current_price)
            })

            self.logger.info(
                f"[{timestamp}] Strategy \"{name}\" closed {direction} qty={qty:.4f} @ {current_price:.2f} → P/L={pnl:,.2f}"
            )
