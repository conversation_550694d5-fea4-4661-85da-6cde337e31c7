2025-07-27 00:17:40,149 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-27 00:17:41,671 - prophet.plot - ERROR - Importing plotly failed. Interactive plots will not work.
2025-07-27 02:00:04,428 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 02:00:04,462 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 02:06:33,618 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 02:06:33,619 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 02:12:42,614 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 02:12:42,615 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 11:13:47,984 - __main__ - ERROR - Fatal error: 'GOOGLE_AI_API_KEY'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Lewis-main\main.py", line 104, in main
    config = Config()
  File "C:\Users\<USER>\Downloads\Lewis-main\config.py", line 22, in __init__
    'GOOGLE_AI_API_KEY': genai.configure(api_key=os.environ['GOOGLE_AI_API_KEY']),
                                                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\os.py", line 717, in __getitem__
    raise KeyError(key) from None
KeyError: 'GOOGLE_AI_API_KEY'
2025-07-27 11:15:34,303 - api_call_manager - INFO - [Queue] Worker loop started.
2025-07-27 11:15:34,410 - api_call_manager - WARNING - [Queue] Worker cancelled.
2025-07-27 11:23:13,675 - __main__ - ERROR - Fatal error: 'GOOGLE_AI_API_KEY'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Lewis-main\main.py", line 104, in main
    config = Config()
  File "C:\Users\<USER>\Downloads\Lewis-main\config.py", line 22, in __init__
    'GOOGLE_AI_API_KEY': genai.configure(api_key=os.environ['GOOGLE_AI_API_KEY']),
                                                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\os.py", line 717, in __getitem__
    raise KeyError(key) from None
KeyError: 'GOOGLE_AI_API_KEY'
2025-07-27 12:07:42,829 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 12:07:42,854 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 12:08:44,315 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:08:44,516 - cmdstanpy - DEBUG - Adding TBB (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-07-27 12:08:44,555 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:08:44,769 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:08:45,494 - api_call_manager - INFO - [Queue] Worker loop started.
2025-07-27 12:08:45,505 - api_call_manager - WARNING - [Queue] Worker cancelled.
2025-07-27 12:10:15,982 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 12:10:15,982 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 12:10:55,704 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:10:55,957 - cmdstanpy - DEBUG - Adding TBB (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-07-27 12:10:55,996 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:10:56,108 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:10:56,226 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:10:56,306 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:10:56,422 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:10:56,501 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:10:56,503 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:10:56,583 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:10:56,624 - __main__ - ERROR - Fatal error: StrategyGenerator.__init__() takes 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Lewis-main\main.py", line 117, in main
    trading_system = TradingSystem(config, processed_data, api_call_manager)
  File "C:\Users\<USER>\Downloads\Lewis-main\trading_system.py", line 40, in __init__
    self.market_maker = MarketMaker(self.config, api_call_manager, strategy_config_path='strategies.json')
                        ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Lewis-main\market_maker.py", line 183, in __init__
    self.strategy_generator = StrategyGenerator(config, api_call_manager)
                              ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: StrategyGenerator.__init__() takes 2 positional arguments but 3 were given
2025-07-27 12:10:57,241 - api_call_manager - INFO - [Queue] Worker loop started.
2025-07-27 12:10:57,254 - api_call_manager - WARNING - [Queue] Worker cancelled.
2025-07-27 12:12:08,615 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 12:12:08,616 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 12:53:13,338 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:53:13,561 - cmdstanpy - DEBUG - Adding TBB (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-07-27 12:53:14,291 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:53:14,483 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:53:14,645 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:53:14,723 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:53:14,841 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:53:14,920 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:53:14,923 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-27 12:53:15,004 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-27 12:53:15,047 - __main__ - ERROR - Fatal error: type object 'datetime.time' has no attribute 'time'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Lewis-main\main.py", line 117, in main
    trading_system = TradingSystem(config, processed_data, api_call_manager)
  File "C:\Users\<USER>\Downloads\Lewis-main\trading_system.py", line 40, in __init__
    self.market_maker = MarketMaker(self.config, api_call_manager, strategy_config_path='strategies.json')
                        ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Lewis-main\market_maker.py", line 216, in __init__
    self.last_health_check = time.time()
                             ^^^^^^^^^
AttributeError: type object 'datetime.time' has no attribute 'time'
2025-07-27 12:53:15,571 - api_call_manager - INFO - [Queue] Worker loop started.
2025-07-27 12:53:15,682 - api_call_manager - WARNING - [Queue] Worker cancelled.
2025-07-27 14:26:25,265 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 14:26:25,282 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 14:31:59,025 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 14:31:59,025 - api_call_manager - INFO - [Queue] Worker started.
2025-07-27 14:40:05,959 - api_call_manager - INFO - [Queue] Attempting to start worker...
2025-07-27 14:40:05,959 - api_call_manager - INFO - [Queue] Worker started.
2025-07-30 18:55:03,087 - __main__ - ERROR - Fatal error: 'GOOGLE_AI_API_KEY'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Lewis-main\main.py", line 104, in main
    config = Config()
  File "C:\Users\<USER>\Downloads\Lewis-main\config.py", line 22, in __init__
    'GOOGLE_AI_API_KEY': genai.configure(api_key=os.environ['GOOGLE_AI_API_KEY']),
                                                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\os.py", line 717, in __getitem__
    raise KeyError(key) from None
KeyError: 'GOOGLE_AI_API_KEY'
